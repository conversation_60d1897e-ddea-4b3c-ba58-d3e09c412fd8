@extends('dashboard.layout.master')
@push("css")
<style>
.chat-status {
    padding: 3px 10px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.chat-active {
    background-color: #d4edda;
    color: #155724;
}

.chat-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin-right: 10px;
}

.job-info {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
}
</style>
@endpush

@section('content')
<section class="projects_wrapper pagination_scroll_tbl">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="custom_projects_tabs scrollable_tbl">
                    <div class="custom_search_filter custom_flex">
                        <h3>Chat Management</h3>
                        <div class="txt_field custom_search">
                            <input type="text" placeholder="Search" class="custom_search_box">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <div class="custom_table">
                            <table id="" class="table table-striped myTable datatable">
                                <thead>
                                <tr>
                                    <th>Buyer</th>
                                    <th>Seller</th>
                                    <th>Job Details</th>
                                    <th>Chat Type</th>
                                    <th>Created Date</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                @forelse($chats as $chat)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ asset('website/assets/images/service_img.png') }}" 
                                                     alt="Buyer" class="user-avatar">
                                                <div>
                                                    <strong>{{ $chat->buyer->name ?? 'N/A' }}</strong><br>
                                                    <small class="text-muted">{{ $chat->buyer->email ?? 'N/A' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ asset('website/assets/images/service_img.png') }}" 
                                                     alt="Seller" class="user-avatar">
                                                <div>
                                                    <strong>{{ $chat->seller->name ?? 'N/A' }}</strong><br>
                                                    <small class="text-muted">{{ $chat->seller->email ?? 'N/A' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="job-info">
                                                <strong>{{ $chat->job->project_title ?? 'N/A' }}</strong><br>
                                                <small class="text-muted">{{ $chat->job->project_number ?? 'N/A' }}</small><br>
                                                <small class="text-success">${{ $chat->job->budget ?? 'N/A' }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="chat-status chat-active">
                                                {{ ucfirst(str_replace('_', ' ', $chat->chat_type)) }}
                                            </span>
                                        </td>
                                        <td>{{ $chat->created_at->format('d M Y, h:i A') }}</td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="viewChatMessages({{ $chat->id }})">
                                                            <i class="fa-solid fa-comments"></i> View Messages
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="viewJobDetails({{ $chat->job_id }})">
                                                            <i class="fa-solid fa-briefcase"></i> View Job
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">No chats found</td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3">
                        {{ $chats->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Chat Messages Modal -->
<div class="modal fade" id="chatMessagesModal" tabindex="-1" role="dialog" aria-labelledby="chatMessagesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chatMessagesModalLabel">Chat Messages</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="chatMessagesContent" style="max-height: 500px; overflow-y: auto;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Job Details Modal -->
<div class="modal fade" id="jobDetailsModal" tabindex="-1" role="dialog" aria-labelledby="jobDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="jobDetailsModalLabel">Job Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="jobDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
function viewChatMessages(chatId) {
    $.ajax({
        url: '{{ url("admin/chat-messages") }}/' + chatId,
        type: 'GET',
        success: function(response) {
            $('#chatMessagesContent').html(response);
            $('#chatMessagesModal').modal('show');
        },
        error: function() {
            alert('Error loading chat messages');
        }
    });
}

function viewJobDetails(jobId) {
    $.ajax({
        url: '{{ url("admin/job-details") }}/' + jobId,
        type: 'GET',
        success: function(response) {
            $('#jobDetailsContent').html(response);
            $('#jobDetailsModal').modal('show');
        },
        error: function() {
            alert('Error loading job details');
        }
    });
}

// Search functionality
$(document).ready(function() {
    $('.custom_search_box').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.myTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
});
</script>
@endpush
