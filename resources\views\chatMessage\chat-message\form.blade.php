<div class="form-group {{ $errors->has('chat_id') ? 'has-error' : ''}}">
    <label for="chat_id" class="col-md-4 control-label">{{ 'Chat Id' }}</label>
    <div class="col-md-6">
        <input class="form-control" name="chat_id" type="text" id="chat_id" value="{{ $chatmessage->chat_id??''}}" >
        {!! $errors->first('chat_id', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('sender_id') ? 'has-error' : ''}}">
    <label for="sender_id" class="col-md-4 control-label">{{ 'Sender Id' }}</label>
    <div class="col-md-6">
        <input class="form-control" name="sender_id" type="text" id="sender_id" value="{{ $chatmessage->sender_id??''}}" >
        {!! $errors->first('sender_id', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('message') ? 'has-error' : ''}}">
    <label for="message" class="col-md-4 control-label">{{ 'Message' }}</label>
    <div class="col-md-6">
        <input class="form-control" name="message" type="text" id="message" value="{{ $chatmessage->message??''}}" >
        {!! $errors->first('message', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('attachment_path') ? 'has-error' : ''}}">
    <label for="attachment_path" class="col-md-4 control-label">{{ 'Attachment Path' }}</label>
    <div class="col-md-6">
        <input class="form-control" name="attachment_path" type="text" id="attachment_path" value="{{ $chatmessage->attachment_path??''}}" >
        {!! $errors->first('attachment_path', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('attachment_type') ? 'has-error' : ''}}">
    <label for="attachment_type" class="col-md-4 control-label">{{ 'Attachment Type' }}</label>
    <div class="col-md-6">
        <input class="form-control" name="attachment_type" type="text" id="attachment_type" value="{{ $chatmessage->attachment_type??''}}" >
        {!! $errors->first('attachment_type', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        <input class="btn btn-primary" type="submit" value="{{ $submitButtonText??'Create' }}">
    </div>
</div>
