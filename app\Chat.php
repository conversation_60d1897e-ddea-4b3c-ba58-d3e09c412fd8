<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Chat extends Model
{
    use SoftDeletes;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'chats';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['job_id', 'buyer_id', 'seller_id', 'chat_type', 'reference_id'];

    public function job() {
        return $this->belongsTo(Job::class, 'job_id');
    }

    public function buyer() {
        return $this->belongsTo(User::class, 'buyer_id');
    }

    public function seller() {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function messages() {
        return $this->hasMany(ChatMessage::class, 'chat_id');
    }

    public function contactRequest() {
        return $this->belongsTo(ContactRequest::class, 'reference_id');
    }

}
