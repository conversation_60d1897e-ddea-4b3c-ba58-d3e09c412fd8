<?php

namespace App\Http\Controllers;

use App\Profile;
use App\User;
use App\Job;
use App\ContactRequest;
use App\Events\UserNotifyEvent;
use Carbon\Carbon;
use Illuminate\Http\Request;

class SellerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    public function sellerChat(){
        return view("website.serviceProvider.service_provider_chat");
    }
    public function jobRequestContact($id){
        $item = Job::where('id',$id)->first();
        $item = ContactRequest::create(['job_id'=>$item->id,'seller_id'=>auth()->user()->id,'status'=>'pending']);
        $adminId = User::find(2);
        $data = [
            'title' => 'Contact Request',
            'type' => 'contact_request'
        ];
        event(new UserNotifyEvent($adminId->id , $data['type'],$data));
        event(new UserNotifyEvent(auth()->user()->id , $data['type'],$data));
        return redirect()->back()->with(['title'=>'Done','message'=>"Your contact request has been sent successfully. We will get back to you shortly!",'type'=>'success']);;
    }
}
