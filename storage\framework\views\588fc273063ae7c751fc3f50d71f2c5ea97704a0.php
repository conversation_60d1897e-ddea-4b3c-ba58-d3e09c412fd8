
<?php $__env->startPush("css"); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="custom_chats">
        <div class="container">
            <div class="row">
                <div class="col-md-4"> 
                    <div class="nav nav-pills chats_detail" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <div class="new_chat">
                            <h3>Inbox</h3>
                        </div>
                        <div class="searchbar_input">
                            <input type="search" id="myinput" class="custom_search_box form-control" placeholder="Search">
                        </div>
                        <div class="all_users_chats myTable">
                            <?php for($i=0;$i<5;$i++): ?>
                                <div class="user_profile_msg nav-link past <?php echo e($i == 0 ? 'active' : ''); ?>" id="v-pills-past-tab<?php echo e($i); ?>" data-bs-toggle="pill" data-bs-target="#v-pills-past<?php echo e($i); ?>" role="tab" aria-controls="v-pills-past<?php echo e($i); ?>" aria-selected="false" tabindex="-1">
                                    <div class="user_messages">
                                        <div class="user_img">
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png">
                                        </div>
                                        <div class="user_msg">
                                            <h6>John Doe</h6>
                                            <span>You: <p>thanks!</p></span>
                                        </div>
                                    </div>
                                    <div class="time">
                                        <p>10:30 Pm</p>
                                        <span class="status">03</span>
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="tab-content" id="v-pills-tabContent">
                        <?php for($i=0;$i<5;$i++): ?>
                            <div class="tab-pane fade <?php echo e($i == 0 ? 'active show' : ''); ?>" id="v-pills-past<?php echo e($i); ?>" role="tabpanel" aria-labelledby="v-pills-past-tab<?php echo e($i); ?>" tabindex="0">
                                <div class="chats_section">
                                    <div class="users_chats">
                                        <div class="user_profile">
                                            <div class="user_img">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png">
                                            </div>
                                            <div class="user_name">
                                                <h5>john Doe</h5>
                                                <p>35 Minutes Ago</p>
                                            </div>
                                            <div class="dispute_btn">
                                                <button type="button" class="btn btn_red" data-bs-toggle="modal" data-bs-target="#seller_chat_dispute">Dispute</button>
                                            </div>
                                        </div>
                                        <div class="chats_wrapper append_input_wrapper">
                                            <div class="chat_messages">
                                                <div class="custom_users_chats">
                                                    <div class="messages">
                                                        <div class="clip received">
                                                            <div class="text">Hello There</div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="text">It is a long established fact that a reader</div>
                                                        </div>
                                                        <div class="profile_owner received">
                                                            <div class="msg-time"> <p>10:11 AM</p></div>
                                                        </div>
                                                        <div class="clip sent">
                                                            <div class="text">Hello There</div>
                                                        </div>
                                                        <div class="clip sent">
                                                            <div class="text">Lorem ipsum is simply a dummy text.
                                                            </div>
                                                        </div>
                                                        <div class="profile_owner sent">
                                                            <div class="msg-time"> <p>10:11 AM</p></div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="text">Hello There</div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="text">It is a long established fact that a reader</div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="upload_file">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/file-pdf-solid.svg">
                                                            </div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="text">It is a long established fact that a reader</div>
                                                        </div>
                                                        <div class="profile_owner received">
                                                            <div class="msg-time"> <p>10:11 AM</p></div>
                                                        </div>
                                                        <div class="clip sent">
                                                            <div class="send_img">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/plumbering.png">
                                                            </div>
                                                        </div>
                                                        <div class="clip sent">
                                                            <div class="text">Lorem ipsum is simply a dummy text.
                                                            </div>
                                                        </div>
                                                        <div class="profile_owner sent">
                                                            <div class="msg-time"> <p>10:11 AM</p></div>
                                                        </div>
                                                        <div class="custom_append_msg">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <form>
                                                <div class="send_msg">
                                                    <input type="text" placeholder="Type Here" class="form-control enter_msg">
                                                    <input type="file" class="form-control">
                                                    <button class="btn_blue send_message" type="button"><i class="fa-solid fa-paper-plane"></i></button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <div class="accept_reject_msg">
                                        <div class="user_profile">
                                            <div class="user_img">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png">
                                            </div>
                                            <div class="user_name">
                                                <h5>john Doe</h5>
                                            </div>
                                        </div>
                                        <div class="new_message">
                                            <p>John Doe would lke to message you</p>
                                        </div>
                                        <div class="custom_btn">
                                            <button type="button" class="btn btn_red" value="rejected">Reject</button>
                                            <button type="button" class="btn btn_green" value="accepted">Accept</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="modal fade dispute_milestone" id="seller_chat_dispute" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Dispute Chat</h3>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Disputed Milestone</label>
                                    <select class="form-control" aria-label="Default select example">
                                        <option selected disabled>Open this select menu</option>
                                        <option value="1">John Doe</option>
                                        <option value="2">Alyan</option>
                                        <option value="3">imtiaz</option>
                                        <option value="3">Umair</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Reason To Dispute</label>
                                    <textarea placeholder="Enter Comments" rows="5" class="form-control"></textarea>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>

<script>
    $(document).ready(function () {
//        var date = new Date();
//        var time = date.toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
//        $(".send_message").click(function (){
//            var msg = $(this).closest(".append_input_wrapper").find('.enter_msg').val();
//            if(msg.trim()!==""){
//                $(this).closest(".append_input_wrapper").find(".custom_append_msg").append('<div class="append_msg">' + '<div class="clip sent">' +
//                    '<div class="text">' + msg  + '</div>' + '</div>' + '<div class="profile_owner sent">' + '<div class="msg-time">' +
//                    '<p>' + time + '</p>' + '</div>' + '</div>' + '</div>');
//                $(this).closest(".append_input_wrapper").find('.enter_msg').val("");
//            };
//        });

        $('.send_msg button').attr('disabled','disabled');
        $('.send_msg input[type="text"]').keyup(function(){
            if($(this).val().length > 0){
                $('.send_msg button').removeAttr('disabled');
            }
            else if($(this).val().length == 0){
                $('.send_msg button').attr('disabled','disabled');
            }
        });

        $(".send_msg input[type=file]").click(function () {
            $(".send_msg input[type=file]").css("width", "150px");
        });

        $(".users_chats").hide();
        $('.accept_reject_msg button').click(function() {
            var btnValue = $(this).val();
            if (btnValue === "accepted") {
                console.log(btnValue);
                $(this).closest('.chats_section').find('.users_chats').show();
                $(this).closest('.chats_section').find(".accept_reject_msg").hide();
            }
            else if (btnValue === "rejected") {
                $(this).closest('.chats_section').find(".accept_reject_msg .new_message p").html("Message Has Been Rejected");
                $(this).closest('.chats_section').find(".accept_reject_msg .custom_btn").hide();
                console.log(btnValue);
            };
        });
        $(".searchbar_input .custom_search_box").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $(".all_users_chats.myTable *").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
        $(".send_msg button").click(function () {
            $(".custom_chats .chats_section .chat_messages").animate({
                scrollTop: $('.custom_chats .chats_section .chat_messages').get(0).scrollHeight
            }, 2000);
        });
    });


</script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mrdoall-git\resources\views/website/serviceProvider/service_provider_chat.blade.php ENDPATH**/ ?>