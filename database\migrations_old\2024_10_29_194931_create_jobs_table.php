<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateJobsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->text('dynamic_question_answer')->nullable();
            $table->string('sub_category_id')->nullable();
            $table->string('user_id')->nullable();
            $table->string('project_budget_min')->nullable();
            $table->string('category_id')->nullable();
            $table->string('project_budget_max')->nullable();
            $table->string('visit_date')->nullable();
            $table->string('visit_time_from')->nullable();
            $table->string('visit_time_to')->nullable();
            $table->string('allow_verified')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('jobs');
    }
}
