<?php

namespace App\Http\Controllers;

use App\AssignSellerExpertise;
use App\Balance;
use App\EstimateOffer;
use App\Events\UserNotifyEvent;
use App\GroupChat;
use App\Job;
use App\JobAssignStaffDocument;
use App\JobAssignStaffMeasurement;
use App\JobMilestone;
use App\DisputeMilestone;
use App\Message;
use App\MessageViewed;
use App\Notification;
use App\Portfolio;
use App\Repositories\NotificationRepositoryInterface;
use App\RequestStaffAssign;
use App\UserAttachment;
use App\JobMilestoneRequestDocument;
use App\JobOffer;
use App\Profile;
use App\RequestWithdrawal;
use App\User;
use App\UserCategory;
use App\UserCategoryRequest;
use App\UserGeographicalPreference;
use App\UserRequest;
use App\UserRestrictedState;
use Auth;
use Carbon\Carbon;
use Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Pion\Laravel\ChunkUpload\Handler\HandlerFactory;
use Pion\Laravel\ChunkUpload\Receiver\FileReceiver;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Illuminate\Support\Facades\Mail;
use Stripe\Checkout\Session;
use Illuminate\Support\Facades\Http;




class AdminController extends Controller
{
    protected $notificationRepo;

    public function __construct(NotificationRepositoryInterface $notificationRepo)
    {
//        $this->notificationRepo = $notificationRepo;
    }

    public function storeStaff(Request $request)
    {
        extract($request->all());
        try {
            foreach ($emails as $email){
                if (!User::where('email',$email)->exists()){
                    if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $name = strstr($email, '@', true); // Get the part before @
                        $password = Str::random(6);
                        $user = User::firstOrCreate([
                                'email' => $email,
                        ],
                            [
                                'name' => $name ?? 'user_' . rand(000, 999),
                                'password' => bcrypt($password),
                                'status' => 1,
                            ]);
                        if ($user->profile == null) {
                            Profile::create([
                                'user_id' => $user->id,
                                'pic' => 'no_avatar.jpg'
                            ]);
                            $user->assignRole('staff');
                            $data =[
                                'name'    => $user->name,
                                'email'   => $user->email,
                                'message' => "Hello {$user->name},<br><br> Your email ID is {$user->email} and password is {$password}.<br><br>",
                                'subject' => 'A new staff account has been created',
                            ];
                            // Send the email with the raw body
                            Mail::send('static_email_templates.user_create_email', ['data' => $data, 'password' => $password], function ($message) use ($data) {
                                $message->to($data['email'])
                                    ->bcc(auth()->user()->email)
                                    ->subject($data['subject']);
                            });
                        }//ends if
                    }//ends if
                }//ends if...
             }//ends foreach
            return back()->with(['title'=>'Done','message'=>'Staff Created successfully','type'=>'success']);
        }catch (\Exception $e){
            return back()->with(['title'=>'Error','message'=>'Error: '.$e->getMessage(),'type'=>'error']);
        }//ends try
    } // Ends storeStaff
    public function assignStaffToJob(Request $request)
    {
        extract($request->all());
        if (isset($assign_staff_id) && !empty($assign_staff_id) && isset($job_id) && !empty($job_id)){
            $job = Job::where('id',$job_id)->first();
//            Job::where('id',$job_id)->update(['assign_staff_id'=>$assign_staff_id]); unaiz code.
            $job->update(['assign_staff_id'=>$assign_staff_id]);
                if (JobAssignStaffMeasurement::where('job_id', $job_id)->where('status', 1)->exists()) {
                JobAssignStaffMeasurement::where('job_id',$job_id)->update([ 'status' => '0' ]);
            }//ends if...
                if (JobAssignStaffDocument::where('job_id', $job_id)->where('status', 1)->exists()) {
                JobAssignStaffDocument::where('job_id',$job_id)->update([ 'status' => '0' ]);
            }//ends if...
                $user = User::where('id', $assign_staff_id)->first();
            if($user){
                $data = [
                    'title'         => 'Job Assigned',
//                    'message'       => 'You have been assigned to a new job.',
                    'message'       => "You’ve been selected by the admin for the job". $job->project_title ?? '' .".Best of luck as you begin the task!",
                    'name'          => $user->name??'',
                    'staff_email'   => $user->email??'',
                    'job_title'     => $job->project_title??'',
                    'min_amount'    => $job->project_budget_min ?? '',
                    'max_amount'    => $job->project_budget_max ?? '',
                    'buyer_name'    => $job->user->name ?? '',
                    'buyer_email'   => $job->user->email ?? '',
                    'job_date'      => $job->visit_date?? '',
                    'from_date'     => $job->visit_time_from?? '',
                    'to_date'       => $job->visit_time_to?? '',
                    'type'          => 'job_assigned',
                    'created_at' => Carbon::now()->format('H:i:s'),

                ];
                event(new UserNotifyEvent($user->id, $data['type'], $data));
            }
            if($job->user_id){
                $data = [
                    'title'         => 'Staff Assigned',
                    'message' => "Hello " . ($job->user->name ?? '') . ", your job has been assigned to " . ($user->name ?? '') . " (" . ($job->project_title ?? '') . ")",
                    'name'          => $user->name??'',
                    'staff_email'   => $user->email??'',
                    'job_title'     => $job->project_title??'',
                    'min_amount'    => $job->project_budget_min ?? '',
                    'max_amount'    => $job->project_budget_max ?? '',
                    'buyer_name'    => $job->user->name ?? '',
                    'buyer_email'   => $job->user->email ?? '',
                    'job_date'      => $job->visit_date?? '',
                    'from_date'     => $job->visit_time_from?? '',
                    'to_date'       => $job->visit_time_to?? '',
                    'type'          => 'job_assigned',
                    'created_at' => Carbon::now()->format('H:i:s'),
                ];
                event(new UserNotifyEvent($job->user_id, $data['type'], $data));
            }

            Mail::send('static_email_templates.assign_staff', ['data' => $data], function ($message) use ($data) {
                $message->to($data['staff_email'])
                    ->cc('<EMAIL>')
                    ->subject('New Project Assigned');
            });
            Mail::send('static_email_templates.staff_visit_date', ['data' => $data], function ($message) use ($data) {
                $message->to($data['staff_email'])
                    ->cc('<EMAIL>')
                    ->subject('Site Visit Schedule');
            });

//                \Log::info('Job Assigned Notification: ', ['data' => $data]);
            return back()->with(['title'=>'Done','message'=>'Staff Assigned successfully','type'=>'success']);
            } else {
            return back()->with(['title'=>'Error','message'=>'Please select a staff','type'=>'error']);
        }//ends if
    }//ends assignStaffToJob
    public function jobUpdateStatus(Request $request, $job_id = null, $status = null){
        extract($request->all());
        if (isset($job_id) && !empty($job_id) && isset($status) && !empty($status))
        {
            $job              = Job::where('id',$job_id)->first();
            $staff            = User::where('id', $job->assign_staff_id)->first();
            $admin            = User::findOrFail(2);
            $notificationType = $status == 'rejected' ? 'job_rejected_staff' : ($status == 'accepted' ? 'job_accepted_staff' : 'job_rejected_staff');
            $notificationData = [
//                'title'      => $status === 'rejected' ? 'Job Rejected' : 'Job Accepted',
                'title'      => $status == 'accepted' ? 'Job Accepted' : 'Job Rejected',
                'name'       => $staff ? $staff->name : 'Unknown',
                'buyer_name' => $job->user->name,
                'job_title'  => $job->project_title,
                'job_status' => $notificationType,
                'type'       => 'job_status_staff',
                'created_at' => Carbon::now()->format('H:i:s'),
            ];
            if ($status == 'rejected')
            {
                $job->update(['staff_status' => $status, 'assign_staff_id'=>null]);
            }else
            {
                $job->update(['staff_status' => $status]);
            }
            $userData = $notificationData;
            $userData['message'] = "Good News! " . (($staff ? ($staff->name ?? 'Staff') : 'Staff') . " has agreed to take on your project: " . ($job->project_title ?? ''));
            $adminData = $notificationData;
            $adminData['message'] = (($staff ? ($staff->name ?? 'Staff') : 'Staff') . " has taken the project: " . ($job->project_title ?? '') . " submitted by". " " . ($job->user->name ?? ''));

            event(new UserNotifyEvent($admin->id, 'job_status_staff', $adminData));
            event(new UserNotifyEvent($job->user_id, 'job_status_staff', $userData));

            if ($status == 'accepted'){
                return back()->with(['title'=>'Done','message'=>'staff status updated successfully','type'=>'success']);
            }else{
                return back()->with(['title'=>'Done','message'=>'staff status updated successfully','type'=>'success']);
            }//ends if
        }else{
            return back()->with(['title'=>'Error','message'=>'Please select a staff','type'=>'error']);
        }//ends if
    }//ends jobUpdateStatus

    public function storeStaffForm(Request $request){
        extract($request->all());
//        $cachedFiles = !empty($data['cachedFiles']) ? json_decode($data['cachedFiles'], true) : [];
        if (!empty($measurement_name) && !empty($measurement_number) && !empty($measurement_units) && !empty($job_id)) {
            foreach($measurement_name as $key => $value) {
                $measurement_id = $request->measurement_id[$key] ?? 0;

                if (!$measurement_id) {
                JobAssignStaffMeasurement::create([
                    'name'     => $value?? null,
                    'number'   => $measurement_number[$key] ?? null,
                    'unit'     => $measurement_units[$key] ?? null,
                    'job_id'   => $job_id ?? null,
                    'staff_id' => auth()->user()->id ?? null,
                    ]);
                } else {
                    $measurement = JobAssignStaffMeasurement::find($measurement_id);
                    if ($measurement) {
                        $measurement->update([
                            'name'     => $value ?? null,
                            'number'   => $measurement_number[$key] ?? null,
                            'unit'     => $measurement_units[$key] ?? null,
                        ]);
                    }
                }
            }

        }

        $cachedFiles = json_decode($request->cachedFiles, true);

        if (isset($request->staff_doc_id) && !empty($request->staff_doc_id)) {
            foreach ($request->staff_doc_id as $key => $id) {
                if (!$id){
                    if (isset($cachedFiles) && isset($cachedFiles[$key])){
                        JobAssignStaffDocument::create([
                            'image' => $cachedFiles[$key]['fileUrl'],
                            'name' => $measurement_image_name[$key] ?? null,
                            'job_id' => $job_id,
                            'staff_id' => auth()->user()->id ?? null,
                        ]);
                    }
                } else{
                    $doc = JobAssignStaffDocument::find($id);
                    if ($doc) {
                        if (isset($cachedFiles) && !empty($cachedFiles)){
                            foreach ($cachedFiles as $file) {
                                $doc->update([
                                    'image' => $file['docId'] == $id ? $file['fileUrl'] : $doc->image,
                                    'name' => $measurement_image_name[$key] ?? $doc->name,
                                ]);

                            }
                        } else{
                            $doc->update([
                                'name' => $measurement_image_name[$key] ?? $doc->name,
                            ]);
                        }

                    }

                }

            }
        }
        if (!empty($job_id) && !empty($specifications)) {
            Job::where('id', $job_id)->update(['staff_specifications' => $specifications /*, 'status' => 'posted'*/]);
        }
        return back()->with(['title'=>'Done','message'=>'Staff Form stored successfully','type'=>'success']);
    }
    public function removeImageStaffInfo($image_id = null)
    {
        $staffMeasurement = JobAssignStaffDocument::find($image_id);
        if ($staffMeasurement) {
            $this->deleteImage($staffMeasurement->image);
            $staffMeasurement->delete();
            return response()->json(['success' => true, 'message' => 'Image removed successfully.']);
        }
        return response()->json(['success' => false, 'message' => 'Image not found.']);
    }
    public function removeMeasurementStaffInfo($measuremt_id = null)
    {
        $assignStaffMeasurement = JobAssignStaffMeasurement::find($measuremt_id);
        if ($assignStaffMeasurement) {
            $assignStaffMeasurement->delete();
            return response()->json(['success' => true, 'message' => 'Measurement removed successfully.']);
        }
            return response()->json(['success' => false, 'message' => 'Image not found.']);
    }
    public function addStaffFurtherInfo($job_id = null, $staff_id = null){
        $job = Job::find($job_id);
        $jobAssignStaffMeasurement = JobAssignStaffMeasurement::where('job_id',$job_id)->where('staff_id',$staff_id)->get();
        $jobAssignStaffDocument = JobAssignStaffDocument::where('job_id',$job_id)->where('staff_id',$staff_id)->get();
        return (string) view('website.ajax.add_staff_further_detail',compact('jobAssignStaffMeasurement','jobAssignStaffDocument','job'));
    }//ends
    public function createMilestone(Request $request){
        extract($request->all());
        $jobMilestone = JobMilestone::create([
            'job_id'          => $job_id,
            'staff_id'        => auth()->user()->id, // staff_id is the seller id
            'title'           => $title ?? null,
            'amount'          => $milestone_amount ?? null,
            'date'            => $date ?? null,
        ]);
        if ($jobMilestone){
            return back()->with(['title'=>'Done','message'=>'Milestone created successfully','type'=>'success']);
        }else{
            return back()->with(['title'=>'Error','message'=>'Error: Milestone not created','type'=>'error']);
        }
    }//ends function createMilestone
    public function editMilestone(Request $request, $milestone_id = null){
        $milestone = JobMilestone::find($milestone_id);
        return view('website.ajax.edit_milestone_ajax',compact('milestone'));
    }//ends function editMilestone

    public function updateMilestone(Request $request)
    {
        $data = $request->all();
        $jobMilestone = JobMilestone::where('id', $data['milestone_id'])->update([
            'title'           => $data['title'] ?? null,
            'amount'          => $data['milestone_amount'] ?? null,
            'date'            => $data['date'] ?? null,
            'staff_id'        => auth()->user()->id,
        ]);
        if ($jobMilestone) {
            return response()->json(['title' => 'Done', 'message' => 'Milestone updated successfully', 'type' => 'success']);
        } else {
            return response()->json(['title' => 'Error', 'message' => 'Error: Milestone not updated', 'type' => 'error']);
        }
    }
    public function viewSellerBidModalAjax(Request $request, $seller_bid_id = null)
    {
        $sellerBid    = JobOffer::find($seller_bid_id);
        $jobMilestone = JobMilestone::where('job_id',$sellerBid->job_id)->where('staff_id',$sellerBid->staff_id)->get();
        return (string) view('website.ajax.view_seller_bid_modal_ajax',compact('sellerBid','jobMilestone'));
    }//ends function viewSellerBidModalAjax

    public function viewSellerEstimatedModalAjax(Request $request, $seller_estimated_id = null){
        $sellerEstimated = EstimateOffer::find($seller_estimated_id);
        return view('website.ajax.view_seller_estimated_modal_ajax',compact('sellerEstimated'));
    }//ends function viewSellerEstimatedModalAjax

    public function deleteMilestone($milestone_id = null){
        JobMilestone::where('id',$milestone_id)->delete();
        return back()->with(['title'=>'Done','message'=>'Milestone deleted successfully','type'=>'success']);
    }//ends function deleteMilestone

    public function updateJobEstimateOfferStatus(Request $request, $estimate_offer_id = null, $status = null)
    {
        $jobEstimateOffer = EstimateOffer::find($estimate_offer_id);
        $jobEstimateOffer->update(['status'=>$status]);
        return back()->with(['title'=>'Done','message'=>'Job Estimate Offer status updated successfully','type'=>'success']);
    }//ends function updateJobEstimateOfferStatus

    public function updateJobOfferStatus(Request $request, $job_offer_id = null, $status = null)
    {
        extract($request->all());
        if (isset($job_offer_id) && !empty($job_offer_id) && isset($status) && !empty($status)){
            $joboffer = JobOffer::find($job_offer_id);
            $joboffer->update(['status'=>$status]);
            if($status == 'accepted'){
                    return redirect(url('project_contract/' . $joboffer->id . '/' . $joboffer->job_id))->with(['title'=>'Done','message'=>'Job Offer status updated successfully','type'=>'success']);
            }else{
                return back()->with(['title'=>'Done','message'=>'Job Offer status updated successfully','type'=>'success']);
            }

        }else{
            return back()->with(['title'=>'Error','message'=>'Please select a job offer','type'=>'error']);
        }//ends if
    }//ends updateJobOfferStatus

    public function checkPaymemt(Request $request){
        $job_offers = JobOffer::where('status_paid','pending')->where('payment_id','')->get();
        foreach ($job_offers as $key => $value) {
             $session = Session::retrieve($session_id);
                if($session->payment_status == 'paid' || $session->payment_status == 'complete'){
                     Job::where('id',$jobOffer->job_id)->update(['status'=>'on_going']);
                     JobOffer::where('id',$value->id)->update(['payment_status'=>'paid']);
                }
        }
    }

    public function stripeCheckoutSuccess(Request $request, $session_id = null)
    {
        try {
            $admin = User::findOrFail(2);
            $session_id = $session_id; // Safer than extract()
            $offer_id = session()->get('job_offer_id');
            $jobOffer = JobOffer::find($offer_id);
            $jobId= $jobOffer->job_id;
            Stripe::setApiKey(env('STRIPE_SECRET'));
            $session = Session::retrieve($session_id); // Fully qualified name
            $jobOffer->payment_id = $session_id;
            $jobOffer->status_paid = 'pending';
            if (in_array($session->payment_status, ['paid', 'complete'])) {
                Job::where('id', $jobOffer->job_id)->update(['status' => 'on_going','assign_seller_id' => $jobOffer->staff_id]); // staff_id is the seller id
                $jobOffer->status_paid  = 'paid';
                $jobOffer->payment_type = 'stripe';
                $data = [
                    'title'        => $jobOffer->getJobDetail->project_title,
                    'name'         => $jobOffer->getJobDetail->user->name,
                    'email'        => auth()->user()->email,
                    'staff_name'   => $jobOffer->getJobDetail->getSeller->name,
                    'staff_email'  => $jobOffer->getJobDetail->getSeller->email,
                    'amount'       => $jobOffer->amount,
                    'payment_type' => 'stripe',
                    'status'       => 'paid',
                ];
                try{
                    Mail::send('static_email_templates.project_awarded', ['data' => $data], function ($message) use ($data) {
                        $message->to($data['email'])
                            ->cc('<EMAIL>')
                            ->subject('Payment Success');
                    });
                    Mail::send('static_email_templates.seller_project_awarded', ['data' => $data], function ($message) use ($data) {
                        $message->to($data['staff_email'])
                            ->cc('<EMAIL>')
                            ->subject('Payment Success');
                    });
                }catch (\Exception $e){
                    return redirect(url('project_contract',[$offer_id,$jobId]))->with(['title' => 'Error', 'message' => 'Something went wrong, try again.', 'type' => 'error']);
                }
                $customData = $data;
                $customData['title'] = 'Bid Accepted';
//                $customData['message'] = 'Payment for job ' . $jobOffer->getJobDetail->project_title . ' has been paid.';
                $customData['message'] = $jobOffer->getJobDetail->user->name .'has successfully accept your bid for the project: ' . $jobOffer->getJobDetail->project_title . '.';
                $customData['type'] = 'job_payment_accepted';
                event(new UserNotifyEvent($admin->id, $customData['type'], $customData));
                event(new UserNotifyEvent($jobOffer->staff_id, $customData['type'], $customData));
                event(new UserNotifyEvent(Auth::user()->id, $customData['type'], $customData));
            }
            $jobOffer->save();

            return redirect(url('my_projects_ongoing_view',[$jobOffer->job_id]))->with(['title'=>'Done','message'=>'Payment proof uploaded successfully, wait for admin response','type'=>'success']);
            return redirect(url('project_contract',[$offer_id,$jobId]))->with(['title' => 'Done', 'message' => 'Payment Processed successfully.', 'type' => 'success']);
        } catch (\Exception $e) {
            return redirect(url('project_contract',[$offer_id,$jobId]))->with(['title' => 'Error', 'message' => 'Something went wrong, try again.', 'type' => 'error']);
            // return $e->getMessage();
        }
    }
    public function stripeCheckoutCancel(Request $request){
        return $request->all();
    }
    public function assignAwardToSeller(Request $request){
        extract($request->all());
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $jobOffer = JobOffer::find($job_offer_id);
        $buyerId = $jobOffer->getJobDetail->user_id;
        session()->put('job_offer_id', $job_offer_id);
        $milestones = JobMilestone::where('staff_id',$jobOffer->staff_id)->where('job_id',$jobOffer->job_id)->get();
        if ($payment_method_type == 'stripe_ach'){
            $checkoutSession = Session::create([
                'payment_method_types' => ['card','us_bank_account'], // Supports 'card', 'us_bank_account', etc.
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => 'Sample Product',
                        ],
                        'unit_amount' => $milestones->sum('amount') * 100, // Amount in cents
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => url('stripe-payment-success').'/{CHECKOUT_SESSION_ID}',
//                'success_url' => 'https://devcustomprojects.online/testing?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => url('/project_contract/' . $jobOffer->id . '/' . $jobOffer->job_id),
            ]);

            return redirect()->to($checkoutSession->url);
        }elseif($payment_method_type == 'wire_transfer'){
            if ($request->hasFile('proof_file')) {
                $image_input = $this->storeImage('proof_files', $request->file('proof_file'));
                $jobOffer->update([
                    'status_paid'  => 'pending',
                    'proof_file'   => $image_input ?? null,
                    'payment_type' => 'wire_transfer' ?? null,
                ]);
                if($jobOffer->status_paid == 'pending'){
                    try{
                    $data = [
                        'project_title'        => $jobOffer->getJobDetail->project_title,
                        'name'         => $jobOffer->getJobDetail->user->name,
//                        'staff_name'   => $jobOffer->getJobDetail->getSeller->name,
//                        'staff_email'  => $jobOffer->getJobDetail->getSeller->email,
                        'amount'       => $jobOffer->amount,
                        'url'          => url('all_projects'),
                        'payment_type' => 'wire_transfer',
                        'email'        => auth()->user()->email,
                       'type'   => 'job_offer_accepted_wire',
                        'created_at' => Carbon::now()->format('H:i:s'),
                    ];
                    $buyerData = $data;
                    $buyerData['message'] = "Hello " . ($jobOffer->getJobDetail->user->name ?? '') . ",<br><br> Your job offer for the project <strong>" . ($jobOffer->getJobDetail->project_title ?? '') . "</strong> has been accepted by the seller. Please upload the payment proof to proceed.<br><br>";
                    }catch (\Exception $e){
                        return back()->with(['title'=>'Error','message'=>'Error: '.$e->getMessage(),'type'=>'error']);

                    }
                    Mail::send('static_email_templates.project_awarded', ['data' => $data], function ($message) use ($data) {
                        $message->to($data['email'])
                            ->cc('<EMAIL>')
                            ->subject('Payment Success');
                    });
                    //dont uncomment
//                    Mail::send('static_email_templates.seller_project_awarded', ['data' => $data], function ($message) use ($data) {
//                        $message->to($data['staff_email'])
//                            ->cc('<EMAIL>')
//                            ->subject('Payment Success');
//                    });
                    event(new UserNotifyEvent($buyerId, $buyerData['type'], $buyerData));
                }

            } else {
                return back()->with([
                    'title' => 'Error',
                    'message' => 'Please upload proof file',
                    'type' => 'error'
                ]);
            }
            return redirect(url('email_verification',$jobOffer->id))->with(['title'=>'Done','message'=>'Payment proof uploaded successfully, wait for admin response','type'=>'success']);
        }//ends if
    }//ends updateJobOfferStatus

    public function milestoneTable($job_id = null){
        $milestones = JobMilestone::where('staff_id',auth()->user()->id)->where('job_id',$job_id)->get();
        return view('website.ajax.milestone_table_view',compact('milestones'));
    }//ends function milestoneTable

        public function updateContractStatus(Request $request, $job_offer_id = null , $status = null){
        extract($request->all());
        $admin = User::findOrFail(2);

        if (isset($job_offer_id) && !empty($job_offer_id) && isset($status) && !empty($status)){
            $jobOffer = JobOffer::find($job_offer_id);

            $jobOffer->update(['status_paid' => $status]);
            if ($status == 'rejected' || $status == 'pending'){
                Job::where('id',$jobOffer->job_id)->update(['status'=>'posted','assign_seller_id' => '' ]);
            }else{
                Job::where('id',$jobOffer->job_id)->update(['status'=>'on_going','assign_seller_id' => $jobOffer->staff_id]); // staff_id is the seller id
                $data = [
                    'title' => 'Wire Payment Accepted',
                    'project_title' => $jobOffer->getJobDetail->project_title,
                    'name' => $jobOffer->getJobDetail->user->name,
                    'amount' => $jobOffer->amount,
                    'payment_type' => 'wire_transfer',
                    'type' => 'job_offer_accepted_wire',
                    'created_at' => Carbon::now()->format('H:i:s'),
                    'message' => 'Payment for job ' . $jobOffer->getJobDetail->project_title . ' has been accepted.',
                ];
                event(new UserNotifyEvent($jobOffer->getJobDetail->user_id, 'job_offer_accepted_wire', $data));

            }//ends if
            return response()->json(['title'=>'Done','message'=>'Contract status updated successfully','type'=>'success']);
        }else{
            return response()->json(['title'=>'Error','message'=>'Please select a job offer','type'=>'error']);
        }//ends if
    }//ends updateContractStatus
    public function jobOfferModalView(Request $request, $job_offer_id = null){
        $jobOfferId = JobOffer::find($job_offer_id);
        $jobMilestone = JobMilestone::where('job_id',$jobOfferId->job_id)->where('staff_id',$jobOfferId->staff_id)->get(); // $jobOfferId->staff_id is the seller _id
        return (string) view('website.ajax.job_offer_modal_view',compact('jobOfferId','jobMilestone'));
    }//ends function viewSellerBidModalAjax

    public function viewMilestoneRequestedDoc(Request $request, $milestone_id = null){
        $jobMilestone = JobMilestoneRequestDocument::where('milestone_id',$milestone_id)->get(); // $jobOfferId->staff_id is the seller _id
        return (string) view('website.ajax.view_milestone_requested_doc',compact('jobMilestone'));
    }//ends function viewSellerBidModalAjax

    public function storeMilestoneRequest(Request $request)
    {
        DB::beginTransaction();
        try {
            $milestone = JobMilestone::find($request->milestone_id);
            foreach ($request->file('files') as $file) {
                $image_input = $this->storeImage('job_milestone_request_document', $file);
                JobMilestoneRequestDocument::create([
                    'milestone_id' => $request->milestone_id,
                    'file' => $image_input ?? null,
                    'status' => 1, // Default status
                ]);
            }

//            $data = [
//                'title'         => 'Milestone Docs Uploaded',
//                'message'       => ($milestone->job->assignSeller->name ?? 'The seller') . ' has successfully uploaded the Job Milestone documents for the project "' . ($milestone->job->project_title ?? 'Project') . '".',
//                'type'          => 'milestoneDocsUploaded',
//                'project_title' => $milestone->job->project_title ?? 'Unnamed Project',
//                'seller_name'   => $milestone->job->assignSeller->name ?? 'Seller',
//            ];
//            event(new UserNotifyEvent($milestone->job->user_id, $data['type'], $data));
            DB::commit();
            return back()->with([
                'title' => 'Done',
                'message' => 'Job Milestone Docs Uploaded',
                'type' => 'success',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with([
                'title' => 'Error',
                'message' => 'Something went wrong. Please try again.',
                'type' => 'error',
            ]);
        }
    }

    public function milestoneUpdatedReleaseStatus(Request $request, $milestone_id = null, $status = null)
    {
        if (!$milestone_id || !$status) {
            return response()->json(['title' => 'Error', 'message' => 'Invalid request parameters.', 'type' => 'error']);
        }

        $jobMilestone = JobMilestone::find($milestone_id);

        if (!$jobMilestone) {
            return response()->json(['title' => 'Error', 'message' => 'Milestone not found.', 'type' => 'error']);
        }

        $existingMilestone = JobMilestone::where('job_id', $jobMilestone->job_id)
            ->where('staff_id', $jobMilestone->staff_id)
            ->where('requested', 'requested')
            ->get();

        if ($jobMilestone->staff_id == auth()->user()->id && $existingMilestone->count() == 0) {
            // Seller requesting milestone release
            $jobMilestone->update(['requested' => $status]);

            $encryptedJobId = Crypt::encryptString($jobMilestone->job->id);
            $data = [
                'title'           => "Milestone Release Request",
                'project_title'   => $jobMilestone->job->project_title,
                'seller_name'     => $jobMilestone->job->assignSeller->name,
                'seller_email'    => $jobMilestone->job->assignSeller->email,
                'milestone_title' => $jobMilestone->title,
                'buyer_name'      => $jobMilestone->job->user->name,
                'link'            => url('my_projects_ongoing_view', [$encryptedJobId]),
                'dispute_link'    => url('my_projects_ongoing_view', [$encryptedJobId]) . '?opendisputeModalMilestone=true',
                'amount'          => $jobMilestone->amount,
                'email'           => $jobMilestone->job->user->email,
                'payment_type'    => $status === 'requested' ? 'milestone_request' : 'milestone_release',
            ];

            // Send email and notification based on status
            if ($status === 'requested') {
                // Seller is requesting milestone release - notify buyer
                Mail::send('static_email_templates.milestone_release', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'])->cc('<EMAIL>')->subject('Milestone Requested');
                });

                Mail::send('static_email_templates.seller_milestone_request', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['seller_email'])->cc('<EMAIL>')->subject('Milestone Requested');
                });

                $buyerData = $data;
                $buyerData['message'] = "{$data['seller_name']} has requested the release of the milestone titled '{$data['milestone_title']}' for the project '{$data['project_title']}'.";
                $buyerData['type'] = 'milestone_request';

                $sellerData = $data;
                $sellerData['message'] = "You have requested the release of the milestone titled '{$data['milestone_title']}' for the project '{$data['project_title']}'.";
                $sellerData['type'] = 'milestone_request';

                event(new UserNotifyEvent($jobMilestone->job->user_id, $buyerData['type'], $buyerData));
                event(new UserNotifyEvent($jobMilestone->staff_id, $buyerData['type'], $sellerData));
            }

            return response()->json(['title' => 'Done', 'message' => 'Your request has been successful', 'type' => 'success']);
        }

        if ($jobMilestone->job->user_id == auth()->user()->id && $existingMilestone->count() == 1) {
            // Buyer releasing milestone
            if ($status == 'released') {
                $amount = $jobMilestone->amount ?? 0; // Original amount
                $adminPercentage = 12; // Admin percentage
                $adminAmount = ($amount * $adminPercentage) / 100; // Admin's portion
                $remainingAmount = $amount - $adminAmount; // Remaining amount after deduction

                $balance = Balance::updateOrCreate(
                    ['seller_id' => $jobMilestone->staff_id],
                    [
                        'seller_id' => $jobMilestone->staff_id,
                        'milestone_amount' => $jobMilestone->amount ?? 0
                    ]
                );

                $balance->current_balance += $remainingAmount; // Increment the withdrawal
                $balance->total_withdrawal += $remainingAmount; // Increment the withdrawal
                $balance->save(); // Save the changes

                // Prepare data for notification and email
                $encryptedJobId = Crypt::encryptString($jobMilestone->job->id);
                $data = [
                    'title'           => "Milestone Released",
                    'project_title'   => $jobMilestone->job->project_title,
                    'seller_name'     => $jobMilestone->job->assignSeller->name,
                    'seller_email'    => $jobMilestone->job->assignSeller->email,
                    'milestone_title' => $jobMilestone->title,
                    'buyer_name'      => $jobMilestone->job->user->name,
                    'link'            => url('my_projects_ongoing_view', [$encryptedJobId]),
                    'amount'          => $jobMilestone->amount,
                    'email'           => $jobMilestone->job->user->email,
                    'payment_type'    => 'milestone_release',
                    'type'            => 'milestone_release',
                ];

                // Send email to seller about milestone release
                Mail::send('static_email_templates.seller_milestone_request', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['seller_email'])->cc('<EMAIL>')->subject('Milestone Requested');
                });
                $buyerData = $data;
                $buyerData['message'] = "You have released the milestone titled '{$data['milestone_title']}' for the project '{$data['project_title']}'.";
                $sellerData = $data;
                $sellerData['message'] = "{$data['buyer_name']} has released the milestone titled '{$data['milestone_title']}' for the project '{$data['project_title']}'.";
                // Create notifications for both buyer and seller
                event(new UserNotifyEvent($jobMilestone->job->user_id, $buyerData['type'], $buyerData));
                event(new UserNotifyEvent($jobMilestone->staff_id, $sellerData['type'], $sellerData));
            }

            $jobMilestone->update(['requested' => $status]);
            return response()->json(['title' => 'Done', 'message' => 'Your request has been successful', 'type' => 'success']);
        }

        return response()->json([
            'title' => 'Warning',
            'message' => 'The previous milestone is currently in process. Please release the first milestone before submitting this request.',
            'type' => 'warning'
        ]);
    }
    public function storeRequestWithdrawal(Request $request)
    {
        try {
            DB::beginTransaction();
            extract($request->all());
            $admin = User::findOrFail(2);
            if (empty($iban_number) || empty($amount) || empty($bank_name) || empty($card_number)) {
//            if (empty($iban_number) || empty($amount) || empty($bank_name) || empty($request->card_last4)) {
                return back()->with([
                    'title' => 'Error',
                    'message' => 'Please fill all fields',
                    'type' => 'error'
                ]);
            }
            if (auth()->user()->CurrentBalance >= $amount) {
                    RequestWithdrawal::create([
                        'seller_id' => auth()->user()->id,
                        'amount'    => $amount ?? 0,
                        'iban'      => $iban_number ?? null,
                        'card'      => $card_number ?? null,
//                        'card_last4'      => $request->card_last4 ?? null,
//                        'stripe_token' => $request->stripe_token ?? null,
                        'bank'      => $bank_name ?? null,
                    ]);
                Balance::where('seller_id',auth()->id())->update([ 'current_balance' => auth()->user()->CurrentBalance - $amount ]);
            } else {
                DB::rollBack();
                return back()->with([
                    'title' => 'Error',
                    'message' => 'The amount you entered is larger than your available withdrawal amount. Please try again.',
                    'type' => 'error'
                ]);

            }
            $data = [
                'title' => 'Withdrawal Request',
                'message' => auth()->user()->name ?? 'A Service Provider' .' has requested a payment of $'. $amount ?? '0',
                'name' => auth()->user()->name,
                'type' => 'withdrawal_request',
                'created_at' => Carbon::now()->format('H:i:s'),
                'amount' => $amount,
            ];
            event(new UserNotifyEvent($admin->id, $data['type'], $data));
            DB::commit();

            return back()->with([
                'title' => 'Success',
                'message' => 'Withdrawal request submitted successfully',
                'type' => 'success'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with([
                'title' => 'Error',
                'message' => 'An error occurred while processing your request. Please try again later.',
                'type' => 'error'
            ]);
        }
    }


    public function withdrawalRequestView(Request $request, $withdrawal_id = null){
        $withdrawalRequests = RequestWithdrawal::find($withdrawal_id);
        return view('website.ajax.withdrawal_request_view', compact('withdrawalRequests'));
    }//ends function withdrawalRequestView

    public function updateWithdrawalRequest(Request $request)
    {
        if ($request->hasFile('file')) {
            $image_input = $this->storeImage('withdrawal_request_proof', $request->file('file'));
        } else {
            $image_input = null;
        }
        $requestWithdrawal = RequestWithdrawal::find($request->withdrawal_request_id);
        if (!$requestWithdrawal)
        {
            return back()->with(['title' => 'Error', 'message' => 'Withdrawal request not found', 'type' => 'error']);
        }
        $balance = Balance::where('seller_id', $requestWithdrawal->seller_id)->first(); // here and rejected status
        $requestWithdrawal->update([
            'status' => $request->status ?? 'pending',
            'file' => $image_input,
        ]);
        if ($request->status == 'approved')
        {
//          $balance = Balance::where('seller_id', $requestWithdrawal->seller_id)->first();
            if ($balance) {
                $balance->decrement('total_withdrawal', $requestWithdrawal->amount);
            } else {
                return back()->with(['title' => 'Error', 'message' => 'Balance not found for the seller', 'type' => 'error']);
            }
        }
        if($request->status == 'rejected'){
            $balance->update(['current_balance' => $balance->current_balance + $requestWithdrawal->amount]);
        }
        return back()->with(['title' => 'Success', 'message' => 'Withdrawal request has been updated successfully', 'type' => 'success']);
    }//ends function updateWithdrawalRequest
    public function updateUserProfile(Request $request, $id){
        $user = User::where('id',Auth::user()->id)->first();
//        return $user->password;
        $profile = Profile::where('user_id', $id)->first();
        $oldPassword = $request->old_password;
        try{
            DB::beginTransaction();
            $user->update([
                'name' => $request->first_name. ' ' .$request->last_name,
                'first_name'=> $request->first_name,
                'last_name' => $request->last_name,

            ]);
            $profile->update([
                'bio' => $request->about,
                'dob'=> $request->dob,
                'address' => $request->address,
                'phone' => $request->phone,
            ]);
            if ($file = $request->file('pic_file')) {
                $extension = $file->extension()?: 'png';
                $destinationPath = public_path() . '/storage/uploads/users/';
                $safeName = str_random(10) . '.' . $extension;
                $file->move($destinationPath, $safeName);
                $profile->update([
                    'pic' => $safeName,
                ]);
            }
        if($request->password){
            if (Hash::check($oldPassword, $user->password)) {
                $user->update([
                    'password' =>  $request->password==null?$user->password:Hash::make($request->password),
                ]);
            } else {
                return redirect()->back()->with(['message'=>'Current Password is Incorrect kindly check!','type'=> 'error','title'=>'Fail']);
            }
        }
            DB::commit();
        return redirect()->back()->with(['message'=>'Profile has been updated!','type'=>'success','title'=>'Done']);
        }catch(\Exception $e){
            DB::rollback();
        return redirect()->back()->with(['message'=>'Error While updating!','type'=>'error','title'=>'Fail']);
        }
    }
    public function checkOldPassword(Request $request){
        $user = Auth::user();
        $oldPassword = $request->old_password;
        if (Hash::check($oldPassword, $user->password)) {
            return response()->json(['match' => true]);
        } else {
            return response()->json(['match' => false]);
        }
    }
    public function sellerApproveReject(Request $request){
        $sellerId    = $request->input('seller_id');
        $status      = $request->input('status');
        $documentIds = $request->input('document_ids');

        foreach ($documentIds as $documentId) {
            $attachment = UserAttachment::where('user_id', $sellerId)
                ->where('id', $documentId)
                ->first();

            if ($attachment) {
                $attachment->update([ 'status' => $status === 'approved' ? 1 : 0, ]);
            }//ends if..
            if ($attachment->status == 1){
                $data = [
                    'title'   => 'Your Document Has Been Approved',
                    'name'    => $attachment->user->name,
                    'email'   => $attachment->user->email,
                    'message' => "Hello {$attachment->user->name}, Your document in the section " . str_replace('_', ' ', $attachment->section) . " has been approved.",
                    'doc'     => str_replace('_', ' ', $attachment->section),
                    'subject' => 'Document Approved',
                    'url'     => url('login'),
                    'type'    => 'DocApproved' ,
                    'created_at' => Carbon::now()->format('H:i:s'),
                ];
            }else{
                $data = [
                    'title'   => 'Your Document Has Been Rejected',
                    'name'    => $attachment->user->name,
                    'email'   => $attachment->user->email,
                    'message' => "Hello {$attachment->user->name}, Unfortunately, your document in the section " . str_replace('_', ' ', $attachment->section) .
                                 "has been rejected. Please re-upload this document.",
                    'doc'     => str_replace('_', ' ', $attachment->section),
                    'subject' => 'Document Rejected',
                    'url'     => url('login'),
                    'type'    => 'DocRejected',
                    'created_at' => Carbon::now()->format('H:i:s'),
                ];
            }//ends if...
//            \Log::info('Notification data:', $data);
            Mail::send('static_email_templates.seller_approve_email', ['data' => $data], function ($message) use ($data) {
                $message->to($data['email'])
                        ->subject($data['subject'])
                        ->setBody($data['message']);
            });
            $docKey = $attachment->section . '_' . $attachment->id;
            $data['doc_key'] = $docKey;
            $existing = Notification::where('notifiable_id', $sellerId)
                ->where('type', $data['type'])
                ->where('data->doc_key', $docKey)
                ->first();

            if ($existing) {
                $existing->update([
                    'data'       => $data,
                    'created_at' => now(),
                ]);
            } else {
                event(new UserNotifyEvent($sellerId, $data['type'], $data));
            }
        }
        return response()->json(['status' => 'Status updated successfully']);
    }
    public function withdrawalServiceProvider(Request $request, $id = null, $disputed_id = null)
    {
        $jobMilestone = JobMilestone::find($id);
        if (!$jobMilestone) {
            return back()->with([
                'title'   => 'Error',
                'message' => 'Error: Job milestone not found',
                'type'    => 'error',
            ]);
        }
        $jobMilestone->update(['requested' => "released"]);
        // Validate and update Dispute Milestone
        if ($disputed_id) {
            $disputeMilestone = DisputeMilestone::find($disputed_id);
            if (!$disputeMilestone) {
                return back()->with([
                    'title'   => 'Error',
                    'message' => 'Error: Dispute milestone not found',
                    'type'    => 'error',
                ]);
            }
            $disputeMilestone->update(['released' => "1"]);
        }

        // Calculate Amount after Admin Deduction
        $amount = $jobMilestone->amount ?? 0;
        $adminPercentage = 12;
        $adminAmount = ($amount * $adminPercentage) / 100;
        $remainingAmount = $amount - $adminAmount;

        // Update Seller's Balance
        $balance = Balance::updateOrCreate(
            ['seller_id' => $jobMilestone->staff_id],
            ['milestone_amount' => $amount]
        );

        // Ensure total_withdrawal and current_balance are properly updated
        $balance->total_withdrawal = ($balance->total_withdrawal ?? 0) + $remainingAmount;
        $balance->current_balance = ($balance->current_balance ?? 0) + $remainingAmount;

        // Save Balance
        if (!$balance->save()) {
            return back()->with([
                'title'   => 'Error',
                'message' => 'Error: Unable to update balance',
                'type'    => 'error',
            ]);
        }
        return back()->with([
            'title'   => 'Success',
            'message' => 'Withdrawal request submitted successfully',
            'type'    => 'success',
        ]);

    }
    public function updateStatusJob(Request $request, $job_id = null, $status = null){
        $job = Job::find($job_id);
        $job->update(['status' => $status]);
        if($job->status == 'completed')
        {
            $cryptedJobId = Crypt::encryptString($job->id);
            $data = [
//                'title'         => $job->project_title,
                'title'         => 'Job Completed',
                'message'       => 'Your project has been completed successfully.',
                'seller_name'   => $job->assignSeller->name,
//                'seller_email'  => $job->assignSeller->email,
                'buyer_name'    => $job->user->name,
                'email'         => $job->user->email,
                'link'          => url('my_projects_ongoing_view', [$cryptedJobId]),
                'feedback_link' => url('my_projects_ongoing_view', [$cryptedJobId]) .'?feedbackService=true',
//                'seller_link'   => url('service_completed_projects' [$cryptedJobId]),
                'explore_link'  => url('buyer_home'),
                'subject'       => 'Project Completed',
            ];
            Mail::send('static_email_templates.project_complete',['data' => $data],function($message) use ($data){
                $message->to($data['email'])
                    ->bcc(auth()->user()->email)
                    ->subject($data['subject']);
            });
//            $this->notificationRepo->createNotification($job->user_id, 'App\User', 'job_complete', $data);
            event(new UserNotifyEvent($job->user_id, 'job_complete', $data));
        }
        if($job->status == 'posted')
        {
            $data = [
                'title'         => 'Job Re-Posted',
                'message'       => "Your " . ($job->project_title ?? 'job') . " has been re-posted successfully.",
                'project_title' => $job->project_title ?? 'job',
                'seller_name'   => $job->assignSeller->name ?? '',
                'buyer_name'    => $job->user->name ?? '',
                'email'         => $job->user->email ?? '',
//                'link'          => url('my_projects_ongoing_view', [$cryptedJobId]),
                'subject'       => 'Project Re-Posted',
                'type'          => 'job_reposted',
                'created_at' => Carbon::now()->format('H:i:s'),
            ];
//            Mail::send('static_email_templates.project_reposted',['data' => $data],function($message) use ($data){
//                $message->to($data['email'])
//                    ->bcc(auth()->user()->email)
//                    ->subject($data['subject']);
//            });
            event(new UserNotifyEvent($job->user_id, 'job_reposted', $data));
        }
        return back()->with(['title' => 'Done', 'message' => 'Job status updated successfully', 'type' => 'success']);
    }//ends function updateStatusJob
    public function jobCompleteCron(Request $request)
    {
        $jobs = Job::where('status', '!=', 'pending')->orderBy('id', 'desc')->get();
        foreach ($jobs as $job) {
            $job = Job::find($job->id);
            $jobMilestones = JobMilestone::where('job_id', $job->id)->where('staff_id', $job->assign_seller_id)->get();// staff_id is equal to seller_id...
            $jobMilestoneCount = $jobMilestones->count();
            $jobMilestoneReleased = $jobMilestones->where('requested', 'released')->count();
            if ($jobMilestoneCount == $jobMilestoneReleased) {
                $lastMilestone = $jobMilestones->sortByDesc('created_at')->first();
                if ($lastMilestone) {
                    $lastMilestoneDate = $lastMilestone->updated_at; // 2024-12-18 22:37:57 format
                    $thirtyDaysLater = $lastMilestoneDate->addDays(30); // Automatically works with the format
                    if (now()->greaterThanOrEqualTo($thirtyDaysLater)) {
                        $job->update(['status' => 'completed']);
                    }
                }
            }
        }
    }//ends
    public function storeGeographicalPreferences(Request $request){  // working code
        $geographyAddresses = $request->input('geography_address');
        $geographyCities    = $request->input('geography_city');
        $geographyStates    = $request->input('geography_state');
        $geographyPostals   = $request->input('geography_postal');
        $geographyCountries = $request->input('geography_country');
        $geographylatitude  = $request->input('latitude');
        $geographylongitude = $request->input('longitude');
        $geographyradius    = $request->input('geography_radius');
//        $restrictedstates   = $request->input('restricted_states');
        UserGeographicalPreference::create([
            'address'               => $geographyAddresses ?? null,
            'city'                  => $geographyCities ?? null,
            'state'                 => $geographyStates ?? null,
            'postal_code'           => $geographyPostals ?? null,
            'country'               => $geographyCountries ?? null,
            'longitude'             => $geographylongitude ?? null,
            'latitude'              => $geographylatitude ?? null,
            'radius'                => $geographyradius ?? null,
//            'restricted_states'     => $restrictedstates ?? null,
            'user_id'               => auth()->id() ?? null,
        ]);
        return back()->with(['title' => 'Done', 'message' => 'submitted address successfully','type' => 'success',]);
    }//ends function storeGeographicalPreferences
    public function storeRestrictedStates(Request $request){
        extract($request->all());
        UserRestrictedState::where('user_id',auth()->id())->delete();
        if (isset($restricted_states) && !empty($restricted_states)){
            foreach ($restricted_states as $state) {
                UserRestrictedState::create([
                    'state' => $state,
                    'user_id' => auth()->id(),
                ]);
            }//ends foreach
        }//ends if
        return back()->with(['title' => 'Done', 'message' => 'Restricted States Update successfully','type' => 'success',]);
    }//ends function storeRestrictedStates
    public function getGeoLocation(Request $request, $geograph_id = null){
        $geographical = UserGeographicalPreference::find($geograph_id);
        return (string) view('website.ajax.geographical_preferences_view',compact('geographical'));
    }//ends function getGeoLocation...
    public function deleteGeoLocation(Request $request, $user_id = null, $location_id = null){
        UserGeographicalPreference::where('id',$location_id)->where('user_id',$user_id)->delete();
        return response()->json(['title' => 'Done', 'message' => 'Location deleted successfully', 'type' => 'success']);
    }//ends function deleteGeoLocation
    public function userSuspendOrBan(Request $request, $user_id = null, $action = null){
        if (!$user_id || !$action) {
            return response()->json(['title' => 'Error', 'message' => 'Invalid request parameters.', 'type' => 'error']);
        }else{
            $user = User::find($user_id);

            if ($action == 'ban') {
                $data = [
                    'title'   => 'Your Account Has Been Banned',
                    'name'    => $user->name,
                    'email'   => $user->email, // User's email address
                    'subject' => 'Account Banned – Access Permanently Restricted',
                    'message' => 'Your account has been permanently banned due to a violation of our Terms of Service.
                      If you believe this was a mistake, please contact our support team for further assistance.',
                    'type' => 'AccountBan'
                ];


                Mail::send('static_email_templates.user_suspend_ban', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'])
                        ->bcc(auth()->user()->email)
                        ->subject($data['subject']);
                });



                $user->update(['user_status' => $action]); // Update the user status
                $user->delete(); // Soft delete the user (if soft deletes are enabled)
            }
            elseif($action == 'suspended'){
                $data = [
                    'title' => 'Account Suspended – Temporary Restriction on Access',
                    'name'    => $user->name,
                    'email' => $user->email,
                    'subject' => 'Your Account Has Been Suspended',
                    'message' => 'Your account has been temporarily suspended due to a violation of our Terms of Service.
                                Please review our policies and contact our support team if you wish to appeal or request more details.',
                    'type' => 'AccountSuspend',
                ];
                Mail::send('static_email_templates.user_suspend_ban',['data'=>$data], function ($message) use ($data) {
                    $message->to($data['email'])
                        ->subject($data['subject']);
                });
//                \Log::info('Noti DATA ', $data);

                $this->notificationRepo->createNotification($user->id, get_class($user), $data['type'], $data);

                $user->update(['user_status' => $action]);
            }//ends if
            return response()->json(['title' => 'Done', 'message' => 'User status updated successfully', 'type' => 'success', 'action' => $action]);
        }//ends if
    }//ends function userSuspendOrBan

    public function checkPasswordvalid(Request $request)
    {
        $password = $request->input('password');
        $user = User::findOrFail(auth()->user()->id);

        if (Hash::check(trim($password), $user->password)) {
            return response()->json(true);
        } else {
            return response()->json("Password does not match");
        }//ends if..

    }//end function checkPasswordvalid


    public function changeUserPassword(Request $request)
    {
        $user = auth()->user();
        if (Hash::check($request->password, $user->password)){
            $user->fill(['password' => Hash::make($request->new_password) ])->save();
            return redirect()->back()->with(['type' => 'Done', 'message' => "password has been changed", 'title' => 'success']);
        } else {
            return redirect()->back()->with(['type' => 'error', 'msg' => "something went wrong", 'title' => 'fail']);
        }
    }//end function changeUserPassword.

    public function profileUpdate(Request $request){
        if (auth()->user()) {
            if (auth()->user()->hasRole('admin') || auth()->user()->hasRole('user')){
                $data = [
                    'first_name'=> $request->first_name,
                    'last_name' => $request->last_name,
                ];
            }else{
                $data = [
                    'name'      => $request->first_name.' '.$request->last_name,
                    'first_name'=> $request->first_name,
                    'last_name' => $request->last_name,
                ];
            }
            if ($file = $request->file('pic_file')) {
                $extension = $file->extension() ?: 'png';
                $destinationPath = public_path() . '/storage/uploads/users/';
                $safeName = str_random(10) . '.' . $extension;
                $file->move($destinationPath, $safeName);
                $image = $safeName;
            } else {
                $image = auth()->user()->profile->pic;
            }//end if
            User::where('id', auth()->user()->id)->update($data);
            Profile::where('user_id', auth()->user()->id)->update([
                'phone'                         => $request->phone ?? auth()->user()->profile->phone,
                'dob'                           => $request->dob ?? auth()->user()->profile->dob,
                'bio'                           => $request->bio ?? auth()->user()->profile->bio,
                'pic'                           => $image,
                'address'                       => $request->address ?? auth()->user()->profile->address,
                'city'                          => $request->city ?? auth()->user()->profile->city,
                'state'                         => $request->state ?? auth()->user()->profile->state,
                'postal'                        => $request->zip_code ?? auth()->user()->profile->postal,
                'country'                       => $request->country ?? auth()->user()->profile->country,
                'suite_or_floor'                => $request->suite_or_floor ?? auth()->user()->profile->suite_or_floor,
                'date_company_was_established'  => $request->date_company_was_established ?? auth()->user()->profile->date_company_was_established,
//                'category_id'                   => $request->category_id ?? auth()->user()->profile->category_id,
                'states_license'                => $request->states_license ?? auth()->user()->profile->states_license,
                'insurance_policy'              => $request->insurance_policy ?? auth()->user()->profile->insurance_policy,
                'insurance_name'                => $request->insurance_name ?? auth()->user()->profile->insurance_name,
                'policy_number'                 => $request->policy_number ?? auth()->user()->profile->policy_number,
                'company_name'                  => $request->company_name ?? auth()->user()->profile->company_name,
            ]);
            return back()->with('flash_message', 'Profile updated!');
        } else {
            return response(view('403'), 403);
        }//end if.
    }//end function profileUpdate.

    public function getPortfolioDetail(Request $request, $port_folio_id = null){
        $portfolio = Portfolio::where('id',$port_folio_id)->first();
        return (string) view('website.ajax.get_portfolio_detail',compact('portfolio'));
    }//ends function getPortfolioDetail

    public function uploadLicenseDocument(Request $request){
        try {
            $user = User::find(auth()->user()->id);
            if (!empty($request->file['input']) && !empty($request->file['section'])) {
                foreach ($request->file['input'] as $index => $file) {
                    if ($file && isset($request->file['section'][$index])) {
                        $section = $request->file['section'][$index];
                        $fileName = $this->storeImage('users_attachment', $file);
                        UserAttachment::updateOrCreate([
                            'user_id' => $user->id,
                            'section' => $section,
                        ],
                            [
                                'user_id' => $user->id,
                                'name'    => $fileName, // Save the file name returned by storeImage
                                'status'  => null,
                                'section' => $section, // Save the associated section
                            ]);
                    }//ends if...
                }//ends foreach...
                return back()->with(['title' => 'Done', 'message' => 'License Document uploaded successfully', 'type' => 'success']);
            }else{
                return back()->with(['title' => 'Error', 'message' => 'Error: License Document not uploaded', 'type' => 'error']);
            }
        }catch (\Exception $e){
            return back()->with(['title' => 'Error', 'message' => 'Error: License Document not uploaded', 'type' => 'error']);
        }
    }//ends function uploadLicenseDocument

    public function requestStaffAssignment(Request $request){
        extract($request->all());
        try {
            DB::beginTransaction();
            if (!isset($job_id) || empty($job_id) || $job_id == 0) {
                return response()->json([
                    'title'   => 'Error',
                    'message' => 'Error: Job not found',
                    'type'    => 'error'
                ]);
            }
            if (RequestStaffAssign::where('job_id', $job_id)->where('user_id', auth()->user()->id)->exists()) {
                return response()->json([
                    'title'   => 'Error',
                    'message' => 'Error: Staff Assignment already requested',
                    'type'    => 'error'
                ]);
            }
            GroupChat::where('group_id',$user_id)->update(['req_staff'=>1]);
            RequestStaffAssign::create([
                'job_id'  => $job_id,
                'user_id' => auth()->user()->id,
                'status'  => 'pending',
            ]);

            DB::commit();
            return response()->json([
                'title'   => 'Done',
                'message' => 'Staff Assignment requested successfully',
                'type'    => 'success'
            ]);
        }catch (\Exception $e){
            DB::rollBack();
            return response()->json([
                'title'   => 'Error',
                'message' => 'Error: Staff Assignment not requested',
                'type'    => 'error'
            ]);

        }
    }//ends function requestStaffAssignment

    public function updateRequestStaffAssignment(Request $request){

        extract($request->all());
        try {
            DB::beginTransaction();

            if (!isset($request_staff_assignment_id) || empty($request_staff_assignment_id) || $request_staff_assignment_id == 0) {
                return response()->json([
                    'title' => 'Error',
                    'message' => 'Error: Job not found',
                    'type' => 'error'
                ]);
            }
            $requestStaffAssign = RequestStaffAssign::find($request_staff_assignment_id);
            $requestStaffAssign->update(['status' => $status]);
            $job = Job::find($requestStaffAssign->job_id);

            if ($status == "approved") {  // just put if approved because of status
                $staff      = User::where('id',$assign_staff_id)->first();
                $group_chat = GroupChat::where('admin_id',$requestStaffAssign->job_id)->get();
                foreach ($group_chat as $key => $value) {
                    $members = json_decode($value->members);
                    array_push($members,$assign_staff_id);
                    GroupChat::where('id',$value->id)->update(['members'=>json_encode($members),'show_group'=>1]);
                        $content = '';
                        $requestData['product_id']      = $requestStaffAssign->job_id;
                        $content .=    '<div class="location_box ">';
                        // $content .=    '<span><i class="fa-solid fa-location-dot"></i></span>';
                        $content .=    '<p>admin assigned '.$staff->name.'</p>';
                        $content .=    '</div>';
                        $requestData['content']         = $content;
                        $requestData['to_user_id']      = '1099022'.$value->id.'';
                        $requestData['from_user_id']    = Auth::user()->id;
                        $requestData['viewed_by_receiver']    = 0;
                        $messages      = Message::create($requestData);
//                        $groupteam = GroupChat::where('group_id','1099022'.$value->id.'')->first();
                        $groupteam = GroupChat::where('group_id', '1099022' . $value->id)->first();
                        $members   = json_decode($groupteam->members);
                        foreach ($members as $key => $membe) {
                            if($membe != Auth::id()){
                                $user = User::where('id',$membe)->first();
                                MessageViewed::create(['product_id'=>$requestStaffAssign->job_id,'send_to'=>Auth::user()->id,'receiver'=>$membe,'is_group'=>1,'viewed'=>'0','group_id'=>'1099022'.$value->id]);
                            }
                        }
                }
                $job->update(['assign_staff_id' => $assign_staff_id]);
                $data = [
                    'title'       => 'Staff Assigned',
                    'message'     =>  " $staff->name  has been assigned successfully to the  $job->project_title .",
                    'job_id'      => $job->id,
                    'user_id'     => $job->user_id,
                    'staff_name'  => $staff->name,
                    'staff_email' => $staff->email,
                    'job_title'   => $job->project_title,
                    'type'        => 'job_staff_assigned',
                ];
                event(new UserNotifyEvent($job->user_id, $data['type'], $data));
                event(new UserNotifyEvent($job->assign_seller_id, $data['type'], $data));

        } else {

            $job->update(['assign_staff_id' => null]);
        }

            DB::commit();

            if(isset($method) && $method == 'ajax'){
                return response()->json([
                    'title' => 'Done',
                    'message' => 'Staff has been assigned successfully',
                    'type' => 'success'
                ]);
            }else{
                return back()->with(['title' => 'Done', 'message' => 'Staff has been assigned successfully', 'type' => 'success']);
            }
        }catch (\Exception $e){
            DB::rollBack();
            return response()->json([
                'title' => 'Error',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'type' => 'error'
            ]);
            \Log::info('Error in requestStaffAssignment: ', ['error' => $e->getMessage(), 'line' => $e->getLine(), 'file' => $e->getFile()]);

        }
    }//ends function requestStaffAssignment


    public function sellerPortfolio(Request $request, $port_folio_id = null){
        $portfolio = Portfolio::find($port_folio_id);
        return (string) view('website.ajax.seller_portfolio',compact('portfolio'));
    }//ends function sellerPortfolio

    public function addSellerExpertise(Request $request){

        extract($request->all());

        if (isset($expertise_id) && !empty($expertise_id)) {
            AssignSellerExpertise::where('user_id', $user_id)->delete();

            foreach ($expertise_id as $expertise) {
                AssignSellerExpertise::create([
                    'user_id' => $user_id,
                    'expertise_id' => $expertise,
                ]);
            }

        }
        return back()->with(['title' => 'Done', 'message' => 'Expertise added successfully', 'type' => 'success']);
    }//ends

    public function userCategoryRequest(Request $request){
        extract($request->all());
        $admin = User::findOrFail(2);
        $userRequest = UserRequest::create([
            'user_id' => auth()->user()->id,
            'status' => 'pending',
        ]);

        if(isset($category_id) && !empty($category_id)){
            foreach ($category_id as $category) {
                UserCategoryRequest::create([
                    'user_request_id' => $userRequest->id,
                    'category_id'     => $category,
                    'status'          => 'pending',
                ]);
            }//ends foreach
        }//ends if

        if(isset($category_id) && !empty($category_id)){
            foreach ($category_id as $category) {
                UserCategory::firstOrCreate([
                    'user_id'         => auth()->user()->id,
                    'category_id'     => $category,
                ],[
                    'user_id'         => auth()->user()->id,
                    'category_id'     => $category,
                    'status'          => 'pending',
                    'is_requested'          => 1,
                ]);
            }//ends foreach
        }//ends if
        $data = [
            'title' => 'User Request',
            'name' => auth()->user()->name,
            'email'   => auth()->user()->email,
            'type' => 'UserRequest',
        ];

        event(new UserNotifyEvent($admin->id,$data['type'],$data));
        return back()->with(['title' => 'Done', 'message' => 'Category request submitted successfully', 'type' => 'success']);
    }//ends function userCategoryRequest

    public function statusUpdateUserRequest(Request $request){  // unaiz bhai code

        extract($request->all());
        if (isset($user_request_id) && !empty($user_request_id)){
            $userRequest = UserRequest::find($user_request_id);
            if (isset($admin_status) && !empty($admin_status) && $admin_status) {
                if (isset($category_request) && !empty($category_request)) {
                    foreach ($category_request as $category_id => $status) {
                        if ($admin_status == 'approved' && $status == 1) {
                            $status_text = ($status == 1) ? 'approved' : 'rejected';
                        } else {
                            $status_text = 'rejected';
                        }
                        UserCategoryRequest::where('user_request_id', $userRequest->id)
                            ->where('category_id', $category_id)
                            ->update(['status' => $status_text]);
                        if ($status_text == 'approved') {
                            UserCategory::where('user_id', $userRequest->user_id)->where('category_id', $category_id)
                                ->update(['status' => $status_text , 'is_requested' => $status == 1 ? 0 : 1 ]);
                        }else{
                            UserCategory::where('user_id', $userRequest->user_id)->where('category_id', $category_id)->delete();
                        }
                    } // ends foreach
                } // ends if
                $data = [
                    'email'   => $userRequest->user->email,
                    'message' => 'Hello ' . $userRequest->user->name . ',<br><br>
                                  Your category request has been updated.Please login to your account to view the updated status.<br><br> Regards, <br> Admin.',
                    'subject' => 'Your category request has been updated',
                    'type' => 'CategoryRequest',
                ];
                // Send the email with the raw body
                Mail::send([], [], function ($message) use ($data) {
                    $message->to($data['email'])
                            ->subject($data['subject'])
                            ->setBody($data['message'], 'text/html');
                });

            }
            $notify = [
                'title'   => 'User Request Status Update',
                'name'    => $userRequest->user->name,
                'email'   => $userRequest->user->email,
                'message' => 'Hello ' . $userRequest->user->name . ',<br><br>
                              Your user request status has been updated. Please login to your account to view the updated status.<br><br> Regards, <br> Admin.',
                'subject' => 'Your user request status has been updated',
                'type' => 'UserRequestStatusUpdate',
            ];
            try {
                event(new UserNotifyEvent($userRequest->user_id, $notify['type'], $notify));
//                    $this->notificationRepo->createNotification($userRequest->user_id, get_class($userRequest->user), $data['type'], $data);
            } catch (\Exception $e) {
                \Log::info('Error creating CustomNotification: ' . $e->getMessage());
                return back()->with(['title' => 'Error', 'message' => $e->getMessage(), 'type' => 'error']);
            }
            $userRequest->update(['status' => $admin_status]);
            return redirect('userRequest/user-request')->with(['title' => 'Done', 'message' => 'User request status updated successfully', 'type' => 'success']);
        } else {
            return back()->with(['title' => 'Error', 'message' => 'Please select a user request', 'type' => 'error']);
        } // ends if
    }//ends function statusUpdateUserRequest

    public function updateVisitStaffDate(Request $request)
    {
        extract($request->all());
        if (isset($job_id) && !empty($job_id)){
            $job = Job::find($job_id);
            if (!empty($visit_time) && !empty($visit_date)) {
                $time_parts = explode(' - ', $visit_time);
                $visit_time_from = isset($time_parts[0])
                    ? Carbon::createFromFormat('g.i a', trim($time_parts[0]))->format('H:i')
                    : null;
                $visit_time_to = isset($time_parts[1])
                    ? Carbon::createFromFormat('g.i a', trim($time_parts[1]))->format('H:i')
                    : null;
            }else{
               return back()->with(['title' => 'Error', 'message' => 'Please select a visit time', 'type' => 'error']);
            }
            $job->update(
                [
                    'visit_date'         => $visit_date ?? null,
                    'visit_time_from'    => $visit_time_from ?? null,
                    'visit_time_to'      => $visit_time_to ?? null,
                ]
            );//ends update
            $data = [
                'name' => $job->user->name,
                'email'   => $job->user->email,
                'message' => 'Hello ' . $job->user->name . ',<br><br>' .
                    'We would like to inform you that the scheduled staff visit date has been updated. Below are the updated details:<br><br>' .
                    'Visit Date: ' . ($visit_date ?? 'Not set') . '<br>' .
                    'Visit Time: ' . ($visit_time_from ?? 'Not set') . ' to ' . ($visit_time_to ?? 'Not set') . '<br><br>' .
                    'If you have any questions or need further information, please feel free to reach out.<br><br>' .
                    'Regards,<br>' .
                    'Admin',
                'subject' => 'Your Staff Visit Schedule Has Been Updated',
                'type' => 'StaffVisitUpdate',
            ];
//            \Log::info('Notification data:', $data);
            // Send the email with HTML body
            Mail::send('static_email_templates.update_visit_staff', ['data'=>$data], function ($message) use ($data) {
                $message->to($data['email'])
                    ->subject($data['subject']);
            });
            try {
                event(new UserNotifyEvent($job->user->id, $data['type'], $data));
            } catch (\Exception $e) {
                Log::error('Error creating CustomNotification: ' . $e->getMessage());
                return back()->with(['title' => 'Error', 'message' => 'There was an issue creating the notification.', 'type' => 'error']);
            }

            return back()->with(['title' => 'Done', 'message' => 'Visit staff date updated successfully', 'type' => 'success']);
        }else{
            return back()->with(['title' => 'Error', 'message' => 'Please select a job', 'type' => 'error']);
        }//ends if
    }//ends function updateVisitStaffDate

    public function staffUploadFileAws(Request $request)
    {
        try {
            $receiver = new FileReceiver('file', $request, HandlerFactory::classFromRequest($request));

            if (!$receiver->isUploaded()) {
                return response()->json(['status' => 'error', 'message' => 'File not uploaded']);
            }

            $save = $receiver->receive();

            if ($save->isFinished()) {
                $file = $save->getFile();
                $ext = $file->getClientOriginalExtension();
                $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $uniqueName = $name . '_' . time() . '.' . $ext;

                // Use AWS SDK multipart upload
                $path = Storage::disk('s3')->putFileAs('staff_job_upload', $file, $uniqueName, 'public');
                $fileUrl = Storage::disk('s3')->url($path);

                // Clean up the temporary file
                $tempFilePath = $file->getPathname();
                if (file_exists($tempFilePath)) {
                    if (!unlink($tempFilePath)) {
                    } else {
                        \Log::info('Temporary file deleted successfully', ['file' => $tempFilePath]);
                    }
                } else {
                    \Log::info('Temporary file does not exist, possibly already deleted', ['file' => $tempFilePath]);
                }

                return response()->json([
                    'message' => 'File uploaded successfully',
                    'files' => [['fileUrl' => $fileUrl]]
                ]);
            }

            $handler = $save->handler();
            return response()->json([
                'done' => $handler->getPercentageDone(),
                'status' => true,
                'chunk' => $handler->getCurrentChunk()
            ]);

        } catch (\Exception $e) {
            // Log the exception for debugging
            \Log::error('File upload failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Upload failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

//    public function threeDayWithoutPosted()
//    {
//        $threeDayWithPosted = Job::where('status', 'pending')
//            ->where('created_at', '<=', now()->addDays(3))
//            ->get();
////        return $threeDayWithPosted;
//        if ($threeDayWithPosted->isNotEmpty()) {
//            foreach ($threeDayWithPosted as $job) {
//                $data = [
//                    'title' => 'Job Reminder',
//                    'name' => $job->user->name,
//                    'email' => $job->user->email,
//                ];
//                Mail::send('static_template_email.days_without_posted', ['data' => $data], function ($message) use ($data) {
//                    $message->to($data['email'])
//                        ->subject('Job Reminder');
//                });
//                $job->update(['status' => 'reminder_sent']);
//            }
//            return response()->json(['message' => 'Reminder emails sent successfully']);
//        } else {
//            return response()->json(['message' => 'No jobs to send reminders for']);
//        }
//    }
//
//
//    public function sevenDayWithoutPosted()
//    {
//        $sevenDayWithoutPosted = Job::where('status', '=', 'pending')
//            ->where('created_at', '<=', now()->subDays(7))
//            ->get();
//        return response()->json(['message' => 'Mail send successfully']);
//    }

    /**
     * Display all contact requests and chats for admin viewing only
     */
    public function contactRequestManagement()
    {
        $contactRequests = ContactRequest::with(['job.user', 'seller'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view("dashboard.admin.contact_request_management", compact('contactRequests'));
    }

    /**
     * View all chats between buyers and sellers
     */
    public function adminChatView()
    {
        $chats = \App\Chat::with(['job', 'buyer', 'seller'])
            ->where('chat_type', 'contact_request')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view("dashboard.admin.admin_chat_view", compact('chats'));
    }



    public function contactRequestView($id)
    {
        $contactRequest = \App\ContactRequest::with(['job', 'seller'])->findOrFail($id);

        $html = '
        <div class="contact-request-details">
            <div class="row">
                <div class="col-md-6">
                    <h6>Seller Information</h6>
                    <p><strong>Name:</strong> ' . ($contactRequest->seller->name ?? 'N/A') . '</p>
                    <p><strong>Email:</strong> ' . ($contactRequest->seller->email ?? 'N/A') . '</p>
                    <p><strong>Phone:</strong> ' . ($contactRequest->seller->phone ?? 'N/A') . '</p>
                </div>
                <div class="col-md-6">
                    <h6>Job Information</h6>
                    <p><strong>Title:</strong> ' . ($contactRequest->job->project_title ?? 'N/A') . '</p>
                    <p><strong>Number:</strong> ' . ($contactRequest->job->project_number ?? 'N/A') . '</p>
                    <p><strong>Budget:</strong> $' . ($contactRequest->job->budget ?? 'N/A') . '</p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <h6>Request Details</h6>
                    <p><strong>Status:</strong> <span class="badge badge-' . ($contactRequest->status === 'pending' ? 'warning' : ($contactRequest->status === 'approved' ? 'success' : 'danger')) . '">' . ucfirst($contactRequest->status) . '</span></p>
                    <p><strong>Request Date:</strong> ' . $contactRequest->created_at->format('d M Y, h:i A') . '</p>
                </div>
            </div>
        </div>';

        return response($html);
    }

    public function chatMessages($id)
    {
        $chat = \App\Chat::with(['messages.sender', 'buyer', 'seller'])->findOrFail($id);

        $html = '
        <div class="chat-messages-container">
            <div class="chat-header mb-3">
                <h6>Chat between ' . ($chat->buyer->name ?? 'Buyer') . ' and ' . ($chat->seller->name ?? 'Seller') . '</h6>
                <small class="text-muted">Project: ' . ($chat->job->project_title ?? 'N/A') . '</small>
            </div>
            <div class="messages-list" style="max-height: 400px; overflow-y: auto;">';

        if ($chat->messages->count() > 0) {
            foreach ($chat->messages as $message) {
                $isBuyer = $message->sender_id === $chat->buyer_id;
                $senderName = $isBuyer ? ($chat->buyer->name ?? 'Buyer') : ($chat->seller->name ?? 'Seller');
                $messageClass = $isBuyer ? 'buyer-message' : 'seller-message';

                $html .= '
                <div class="message-item ' . $messageClass . ' mb-2">
                    <div class="message-header">
                        <strong>' . $senderName . '</strong>
                        <small class="text-muted">' . $message->created_at->format('d M Y, h:i A') . '</small>
                    </div>
                    <div class="message-content">
                        <p>' . nl2br(e($message->message)) . '</p>';

                if ($message->attachment_path) {
                    $html .= '<div class="attachment">
                        <i class="fa fa-paperclip"></i>
                        <a href="' . asset($message->attachment_path) . '" target="_blank">View Attachment</a>
                    </div>';
                }

                $html .= '</div></div>';
            }
        } else {
            $html .= '<p class="text-center text-muted">No messages yet</p>';
        }

        $html .= '</div></div>';

        return response($html);
    }

    public function jobDetails($id)
    {
        $job = Job::with(['user', 'category'])->findOrFail($id);

        $html = '
        <div class="job-details">
            <div class="row">
                <div class="col-md-12">
                    <h6>' . ($job->project_title ?? 'N/A') . '</h6>
                    <p class="text-muted">' . ($job->project_number ?? 'N/A') . '</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Budget:</strong> $' . ($job->budget ?? 'N/A') . '</p>
                    <p><strong>Category:</strong> ' . ($job->category->name ?? 'N/A') . '</p>
                    <p><strong>Status:</strong> ' . ucfirst($job->status ?? 'N/A') . '</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Posted by:</strong> ' . ($job->user->name ?? 'N/A') . '</p>
                    <p><strong>Posted on:</strong> ' . ($job->created_at ? $job->created_at->format('d M Y') : 'N/A') . '</p>
                    <p><strong>Deadline:</strong> ' . ($job->deadline ? date('d M Y', strtotime($job->deadline)) : 'N/A') . '</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <h6>Description</h6>
                    <p>' . nl2br(e($job->description ?? 'No description available')) . '</p>
                </div>
            </div>
        </div>';

        return response($html);
    }


}//ends class AdminController.

