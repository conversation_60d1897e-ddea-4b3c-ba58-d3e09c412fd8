<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Contact;
Route::get('test', 'WebsiteController@test');
Route::post('test', 'WebsiteController@test');

Route::get('/', function () {
    return redirect(url('home'));
    return view('website.main.home');
});


Route::get('/logout', function () {
    Auth::logout();
    return redirect('/');
});


//Route::get('/dashboard', function () {
//    return view('dashboard.index');
//});

Route::group(['middleware' => ['auth', 'roles'], 'roles' => ['admin', 'user','staff','buyer','seller']], function () {

    /*
        Route::get('/dashboard', function () {
            return Auth::user();
            return view('dashboard.index');
        });
    */
    Route::post('status_update_user_request', 'AdminController@statusUpdateUserRequest')->name('status_update_user_request');


    Route::get('my_profile/{user_id?}', 'WebsiteController@myProfile')->name('my_profile');

    Route::post('profile_update','AdminController@profileUpdate')->name('profile_update');
    Route::post('change_user_password','AdminController@changeUserPassword')->name('change_user_password');
    Route::post('/checkPassword', 'AdminController@checkpasswordvalid')->name('check_password');
    Route::get('account-settings', 'UsersController@getSettings');
    Route::post('account-settings', 'UsersController@saveSettings');
    Route::get('projects', 'WebsiteController@projects')->name('projects');
    Route::get('projects_ongoing_view/{job_id?}', 'WebsiteController@projectsOngoingView')->name('projects_ongoing_view');
    Route::get('document', 'WebsiteController@document')->name('document');
    Route::get('staff_projects/{is_staff_required?}', 'WebsiteController@staffProjects')->name('staff_projects');
    Route::get('projects_pending/{job_id?}', 'WebsiteController@projectsPending')->name('projects_pending');
    Route::get('staff_project_details/{job_id?}', 'WebsiteController@staffProjectDetails')->name('staff_project_details');
    Route::get('admin_staff_management', 'WebsiteController@adminStaffManagement')->name('admin_staff_management');
    Route::get('admin_staff_view_management/{staff_id?}', 'WebsiteController@adminStaffViewManagement')->name('admin_staff_view_management');
    Route::get('dispute', 'WebsiteController@dispute')->name('dispute');
    Route::get('admin_chat_management', 'WebsiteController@adminChatManagement')->name('admin_chat_management');
    Route::get('admin_dispute_management', 'WebsiteController@adminDisputeManagement')->name('admin_dispute_management');
    Route::get('admin_ewallet', 'WebsiteController@adminEwallet')->name('admin_ewallet');
    Route::get('user_view/{user_id?}', 'WebsiteController@userServiceProvider')->name('user_view');
    Route::get('user_buyer', 'WebsiteController@userBuyer')->name('user_buyer');
    Route::get('staff_dispute', 'WebsiteController@staffDispute')->name('staff_dispute');
    Route::get('dashboard', 'WebsiteController@dashboard')->name('dashboard');
    Route::get('service_completed_projects/{job_id?}', 'WebsiteController@ServiceCompletedProjects')->name('service_completed_projects');
    Route::get('request_staff_assignment', 'AdminController@requestStaffAssignment')->name('request_staff_assignment');
    Route::get('service_profile/{id?}', 'WebsiteController@serviceProfile')->name('service_profile');
    Route::get('seller_portfolio/{id?}', 'AdminController@sellerPortfolio')->name('seller_portfolio');
    Route::post('add_seller_expertise', 'AdminController@addSellerExpertise')->name('add_seller_expertise');
    Route::post('update_visit_staff_date', 'AdminController@updateVisitStaffDate')->name('update_visit_staff_date');

    Route::get('update_request_staff_assignment', 'AdminController@updateRequestStaffAssignment')->name('update_request_staff_assignment');

    // Route::get('admin_dispute_chat', 'WebsiteController@adminDisputeChat')->name('admin_dispute_chat');
    Route::get('admin_chat', 'WebsiteController@adminChat')->name('admin_chat');
    Route::get('content_management_system', 'WebsiteController@contentManagementSystem')->name('content_management_system');
    Route::get('further_details', 'WebsiteController@furtherDetails')->name('further_details');
    Route::get('get_sub_category', 'WebsiteController@getSubCategory')->name('get_sub_category');
    Route::get('get_sub_category_question/{id?}', 'WebsiteController@getSubCategoryQuestion')->name('get_sub_category_question');
    Route::get('get_sub_category_to_question/{category_id?}', 'JobQuestion\\JobQuestionController@getSubCategoryToQuestion')->name('get_sub_category_to_question');
    Route::get('admin_contracts', 'WebsiteController@adminContracts')->name('admin_contracts');
    Route::get('staff_calendar', 'WebsiteController@staffCalendar')->name('staff_calendar');
    Route::get('download_all_document/{id?}', 'WebsiteController@downloadAllDocument')->name('download_all_document');
    Route::post('store_job_question', 'JobQuestion\\JobQuestionController@storeJobQuestion')->name('store_job_question');
    Route::post('upload_files_aws', 'JobQuestion\\JobQuestionController@uploadFilesAws')->name('upload_files_aws');
    Route::post('delete-file-aws', 'JobQuestion\\JobQuestionController@deleteFileFromAws')->name('delete.file.aws');
    Route::post('store_staff', 'AdminController@storeStaff')->name('store_staff');
    Route::get('user_suspend_or_ban/{user_id?}/{action?}', 'AdminController@userSuspendOrBan')->name('user_suspend_or_ban');
    Route::post('assign_staff_to_job', 'AdminController@assignStaffToJob')->name('assign_staff_to_job');
    Route::post('store_staff_form', 'AdminController@storeStaffForm')->name('store_staff_form');
    Route::post('staff-upload-file-aws', 'AdminController@staffUploadFileAws')->name('staff_upload_file_aws');
    Route::get('add_staff_further_info/{job_id?}/{staff_id?}', 'AdminController@addStaffFurtherInfo')->name('add_staff_further_info');
    Route::get('remove-image-staff-info/{image_id?}', 'AdminController@removeImageStaffInfo')->name('remove_image_staff_info');
    Route::get('remove-measurement-staff-info/{measurement_id?}', 'AdminController@removeMeasurementStaffInfo')->name('remove_measurement_staff_info');
    Route::post('store_job_offer', 'ChatboxController@storeJobOffer')->name('store_job_offer');
    Route::post('estimate_job_offer', 'ChatboxController@estimateJobOffer')->name('estimate_job_offer');
    Route::post('create_milestone', 'AdminController@createMilestone')->name('create_milestone');
    Route::post('update_milestone', 'AdminController@updateMilestone')->name('update_milestone');
    Route::get('job_update_status/{job_id?}/{status?}', 'AdminController@jobUpdateStatus')->name('job_update_status');
    Route::get('edit_milestone/{milestone_id?}', 'AdminController@editMilestone')->name('edit_milestone');
    Route::get('view_seller_bid_modal_ajax/{seller_bid_id?}', 'AdminController@viewSellerBidModalAjax')->name('view_seller_bid_modal_ajax');
    Route::get('view_seller_estimated_modal_ajax/{seller_estimated_id?}', 'AdminController@viewSellerEstimatedModalAjax')->name('view_seller_estimated_modal_ajax');
    Route::get('delete_milestone/{milestone_id?}', 'AdminController@deleteMilestone')->name('delete_milestone');
    Route::get('update_job_offer_status/{job_offer_id?}/{status?}', 'AdminController@updateJobOfferStatus')->name('update_job_offer_status');
    Route::get('update_job_estimate_offer_status/{job_estimate_offer_id?}/{status?}', 'AdminController@updateJobEstimateOfferStatus')->name('update_job_estimate_offer_status');
    Route::get('update-contract-status/{job_offer_id?}/{status?}', 'AdminController@updateContractStatus')->name('update_contract_status');
    Route::get('milestone_table/{job_id?}', 'AdminController@milestoneTable')->name('milestone_table');
    Route::post('assign_award_to_seller', 'AdminController@assignAwardToSeller')->name('assign_award_to_seller');
    Route::get('job_offer_modal_view/{job_offer_id?}', 'AdminController@jobOfferModalView')->name('job_offer_modal_view');
    Route::get('posted_view/{job_id?}', 'WebsiteController@postedView')->name('posted_view');
    Route::post('store_milestone_request', 'AdminController@storeMilestoneRequest')->name('store_milestone_request');
    Route::get('view_milestone_requested_doc/{milestone_id?}', 'AdminController@viewMilestoneRequestedDoc')->name('view_milestone_requested_doc');
    Route::get('milestone_updated_release_status/{milestone_id?}/{status?}', 'AdminController@milestoneUpdatedReleaseStatus')->name('milestone_updated_release_status');
    Route::post('store_request_withdrawal', 'AdminController@storeRequestWithdrawal')->name('store_request_withdrawal');
    Route::post('update_withdrawal_request', 'AdminController@updateWithdrawalRequest')->name('update_withdrawal_request');
    Route::get('withdrawal_request_view/{withdrawal_id?}', 'AdminController@withdrawalRequestView')->name('withdrawal_request_view');
    Route::get('stripe-payment-success/{session_id?}', 'AdminController@stripeCheckoutSuccess')->name('stripe_checkout_success');
    Route::get('stripe-payment-cancel', 'AdminController@stripeCheckoutCancel')->name('stripe_checkout_cancel');
    Route::post('store_review_rating', 'WebsiteController@storeReviewRating')->name('store_review_rating');
    Route::get('update_status_job/{job_id?}/{status?}', 'AdminController@updateStatusJob')->name('update_status_job');

      Route::get('/escalate_to_staff_job','ChatboxController@escalateToStaffJob')->name('escalate_to_staff_job');
      Route::get('/resolve_dispute','ChatboxController@resolveDispute')->name('resolve_dispute');
      Route::get('/dispute_milestone_chat','ChatboxController@disputeMilestoneChat')->name('dispute_milestone_chat');
      Route::get('/dispute_milestone','ChatboxController@disputeMilestone')->name('dispute_milestone');
      Route::post('/get_message_process_cus','ChatboxController@getMessageProcess')->name('get_message_process_cus');
      Route::post('/owner_chat_box','ChatboxController@ownerChatBox')->name('owner_chat_box');
      Route::get('/get_message_process_cus','ChatboxController@getMessageProcess')->name('get_message_process_cus');
      Route::get('/withdrawal_service_provider/{milestone_id?}/{dispute_id?}','AdminController@withdrawalServiceProvider')->name('withdrawal_service_provider');
      Route::post('/send_message_process_cus','ChatboxController@sendMessageProcess')->name('send_message_process_cus');
      Route::get('/get_user_notifications','ChatboxController@getUserNotifications')->name('get_user_notifications');
      Route::get('/get_admin_reward_notification','ChatboxController@getAdminRewardNotification')->name('get_admin_reward_notification');
      Route::get('/get_user_reward_notification','ChatboxController@getuserRewardNotification')->name('get_user_reward_notification');
      Route::get('/buyer_chat/{id?}', 'ChatboxController@index')->name('chat');
      Route::get('/service_provider_chat/{id?}', 'ChatboxController@index')->name('service_provider_chat');
      Route::post('uplaod_image_cus','ChatboxController@uplaod_image')->name('uplaod_image_cus');
      Route::get('get_side_bar_user','WebsiteController@getSideBarUser')->name('get_side_bar_user');
      Route::post('accept_message','ChatboxController@acceptMessage')->name('accept_message');
      Route::get('request_contact/{id?}','ChatboxController@requestContact')->name('request_contact');
      Route::get('estimate_bid_accept/{id?}','ChatboxController@EstimateBidAccept')->name('estimate_bid_accept');
      Route::get('staff_chat/{id?}', 'ChatboxController@staffChat')->name('staff_chat');
      Route::get('admin_dispute_chat/{id?}', 'ChatboxController@staffChat')->name('admin_dispute_chat');
      // end
      Route::post('decision_dispute_staff','ChatboxController@decisionDisputeStaff')->name('decision_dispute_staff');
      Route::get('resolve_dispute_by_user','ChatboxController@resolveDisputeByUser')->name('resolve_dispute_by_user');

        Route::get('dispute_chat', 'WebsiteController@disputeChat')->name('dispute_chat');
        Route::get('website_notification', 'WebsiteController@websiteNotification')->name('website_notification');

        Route::get('project_contract/{job_offer_id?}/{job_id?}', 'WebsiteController@projectContract')->name('project_contract');
        Route::post('/notifications/mark-as-read/{notificationId?}', 'WebsiteController@markAsRead')->name('mark_as_read');
        Route::post('/notifications/read-all', 'WebsiteController@markAllAsRead')->name('mark_all_as_read');

        Route::resource('userAttachment/user-attachment', 'UserAttachment\\UserAttachmentController');
        Route::resource('salutation/salutation', 'Salutation\\SalutationController');
        Route::resource('industry/industry', 'Industry\\IndustryController');
        Route::resource('jobCategory/job-category', 'JobCategory\\JobCategoryController');
        Route::resource('jobSubcategory/job-subcategory', 'JobSubcategory\\JobSubcategoryController');
        Route::resource('jobQuestion/job-question', 'JobQuestion\\JobQuestionController');


        Route::resource('job/job', 'Job\\JobController');
        Route::resource('jobFile/job-file', 'JobFile\\JobFileController');
        Route::resource('jobAssignStaffDocument/job-assign-staff-document', 'JobAssignStaffDocument\\JobAssignStaffDocumentController');
        Route::resource('jobAssignStaffMeasurement/job-assign-staff-measurement', 'JobAssignStaffMeasurement\\JobAssignStaffMeasurementController');
        Route::resource('jobOffer/job-offer', 'JobOffer\\JobOfferController');
        Route::resource('jobMilestone/job-milestone', 'JobMilestone\\JobMilestoneController');
        Route::resource('jobPayment/job-payment', 'JobPayment\\JobPaymentController');
        Route::resource('message/message', 'Message\\MessageController');
        Route::resource('groupChat/group-chat', 'GroupChat\\GroupChatController');
        Route::resource('messageViewed/message-viewed', 'MessageViewed\\MessageViewedController');

        Route::resource('jobMilestoneRequestDocument/job-milestone-request-document', 'JobMilestoneRequestDocument\\JobMilestoneRequestDocumentController');
        Route::resource('balance/balance', 'Balance\\BalanceController');
        Route::resource('requestWithdrawal/request-withdrawal', 'RequestWithdrawal\\RequestWithdrawalController');

        Route::put('user_profile_update/{id}','AdminController@updateUserProfile')->name('user_profile_update');
        Route::post('check-old-password','AdminController@checkOldPassword')->name('check-old-password');
        Route::post('update-status','AdminController@sellerApproveReject')->name('update-status');
        Route::resource('disputeMilestone/dispute-milestone', 'DisputeMilestone\\DisputeMilestoneController');
        Route::resource('portfolio/portfolio', 'Portfolio\\PortfolioController');
        Route::resource('sellerExpertise/seller-expertise', 'SellerExpertise\\SellerExpertiseController');
        Route::resource('ratingReview/rating-review', 'RatingReview\\RatingReviewController');

});
   Route::get('contact_us', 'WebsiteController@contactUs')->name('contact_us');
   Route::get('email_verification/{job_offer_id?}', 'WebsiteController@emailVerification')->name('email_verification');

   //Buyer Dashboard
Route::group(['middleware' => ['auth', 'roles'], 'roles' => [ 'buyer']], function () {
    Route::get('buyer_home', 'WebsiteController@buyerHome')->name('buyer_home');
    Route::get('details_review/{id?}', 'WebsiteController@detailsReview')->name('detailsReview');
    Route::get('post_project', 'WebsiteController@postProject')->name('post_project');
    Route::get('my_projects', 'WebsiteController@myProjects')->name('my_projects');
//    Route::get('my_profile/{user_id?}', 'WebsiteController@myProfile')->name('my_profile');
    Route::get('edit_profile', 'WebsiteController@editProfile')->name('edit_profile');
    Route::get('my_projects_ongoing_view/{job_id?}', 'WebsiteController@myProjectsOngoingView')->name('my_projects_ongoing_view');
    Route::get('my_projects_completed_view/{job_id?}', 'WebsiteController@myProjectsCompletedView')->name('my_projects_completed_view');
    Route::get('plumbing', 'WebsiteController@plumbing')->name('plumbing');
    Route::get('buyer-chat', 'BuyerController@buyerChat')->name('buyer-chat');
    Route::get('contact-requests', 'BuyerController@contactRequests')->name('buyer.contact-requests');
    Route::put('contact-request/{id}/update', 'BuyerController@updateContactRequestStatus')->name('buyer.contact-request.update');
});

//Global Routes

Route::get('notifications', 'WebsiteController@notifications')->name('notifications');



//Service Provider
Route::group(['middleware' => ['auth', 'roles'], 'roles' => [ 'seller']], function () {

    Route::get('seller_home', 'WebsiteController@serviceProviderExplore')->name('seller_home');
    Route::get('get_portfolio_detail/{seller_portfolio_id?}', 'AdminController@getPortfolioDetail')->name('get_portfolio_detail');
    Route::post('store_geographical_preferences', 'AdminController@storeGeographicalPreferences')->name('store_geographical_preferences');
    Route::get('get_geo_location/{geograph_id?}', 'AdminController@getGeoLocation')->name('get_geo_location');
    Route::post('store_restricted_states', 'AdminController@storeRestrictedStates')->name('store_restricted_states');
    Route::get('explore', 'WebsiteController@explore')->name('explore');
    Route::get('delete_geo_location/{user_id?}/{loc_id?}', 'AdminController@deleteGeoLocation')->name('delete_geo_location');
    Route::get('explore_view_project/{job_id?}', 'WebsiteController@exploreViewProject')->name('explore_view_project');
    Route::get('profile_setting', 'WebsiteController@profileSetting')->name('profile_setting');
//    Route::get('service_profile/{id?}', 'WebsiteController@serviceProfile')->name('service_profile');
    Route::get('all_projects', 'WebsiteController@allProjects')->name('all_projects');
    Route::get('ewallet', 'WebsiteController@ewallet')->name('ewallet');
    Route::get('service_provider_projects', 'WebsiteController@ServiceProviderProjects')->name('service_provider_projects');
  //Route::get('service_provider_chat', 'WebsiteController@ServiceProviderChat')->name('service_provider_chat');
//    Route::get('service_completed_projects/{job_id?}', 'WebsiteController@ServiceCompletedProjects')->name('service_completed_projects');
    Route::get('services_bid_offer/{job_id?}', 'WebsiteController@servicesBidOffer')->name('services_bid_offer');
    Route::get('estimate_bid_offer/{job_id?}', 'WebsiteController@estimateBidOffer')->name('estimate_bid_offer');
    Route::get('seller_bids', 'WebsiteController@sellerBids')->name('seller_bids');
    Route::get('view_sellers_bid/{job_id?}', 'WebsiteController@viewSellersBid')->name('view_sellers_bid');
    Route::post('edit-portfolio', 'WebsiteController@editPortfolio')->name('edit-portfolio');
    Route::post('update-portfolio', 'WebsiteController@updatePortfolio');
    Route::post('edit_seller_profile_store', 'AdminController@editSellerProfileStore')->name('edit_seller_profile_store');
    Route::get('seller-profile-edit', 'WebsiteController@sellerProfileEdit')->name('seller-profile-edit');
    Route::get('remove-image-portfolio/{portfolio_id?}', 'WebsiteController@removeImagePortfolio')->name('remove-image-portfolio');
    Route::post('upload-license-document', 'AdminController@uploadLicenseDocument')->name('upload-license-document');
    Route::post('user_category_request', 'AdminController@userCategoryRequest')->name('user_category_request');
    Route::get('seller-chat', 'SellerController@sellerChat')->name('seller-chat');
    Route::get('job-request-contact/{id?}', 'SellerController@jobRequestContact')->name('job-request-contact');
});

Route::group(['middleware' => ['auth', 'roles'], 'roles' =>  ['admin', 'user']], function () {
    #User Management routes
    Route::get('users', 'UsersController@getIndex');
    Route::get('user/create', 'UsersController@create');
    Route::post('user/create', 'UsersController@save');
    Route::get('user/edit/{id}', 'UsersController@edit');
    Route::post('user/edit/{id}', 'UsersController@update');
    Route::get('user/delete/{id}', 'UsersController@delete');
    Route::get('user/deleted/', 'UsersController@getDeletedUsers');
    Route::get('user/restore/{id}', 'UsersController@restoreUser');
});


Route::group(['middleware' => ['auth', 'roles'], 'roles' => 'admin'], function () {
    Route::get('index2', function () {
        return view('dashboard.index2');
    });
    Route::get('index3', function () {
        return view('dashboard.index3');
    });
    Route::get('index4', function () {
        return view('ecommerce.index4');
    });
    Route::get('products', function () {
        return view('ecommerce.products');
    });
    Route::get('product-detail', function () {
        return view('ecommerce.product-detail');
    });
    Route::get('product-edit', function () {
        return view('ecommerce.product-edit');
    });
    Route::get('product-orders', function () {
        return view('ecommerce.product-orders');
    });
    Route::get('product-cart', function () {
        return view('ecommerce.product-cart');
    });
    Route::get('product-checkout', function () {
        return view('ecommerce.product-checkout');
    });
    Route::get('panels-wells', function () {
        return view('ui-elements.panels-wells');
    });
    Route::get('panel-ui-block', function () {
        return view('ui-elements.panel-ui-block');
    });
    Route::get('portlet-draggable', function () {
        return view('ui-elements.portlet-draggable');
    });
    Route::get('buttons', function () {
        return view('ui-elements.buttons');
    });
    Route::get('tabs', function () {
        return view('ui-elements.tabs');
    });
    Route::get('modals', function () {
        return view('ui-elements.modals');
    });
    Route::get('progressbars', function () {
        return view('ui-elements.progressbars');
    });
    Route::get('notification', function () {
        return view('ui-elements.notification');
    });
    Route::get('carousel', function () {
        return view('ui-elements.carousel');
    });
    Route::get('user-cards', function () {
        return view('ui-elements.user-cards');
    });
    Route::get('timeline', function () {
        return view('ui-elements.timeline');
    });
    Route::get('timeline-horizontal', function () {
        return view('ui-elements.timeline-horizontal');
    });
    Route::get('range-slider', function () {
        return view('ui-elements.range-slider');
    });
    Route::get('ribbons', function () {
        return view('ui-elements.ribbons');
    });
    Route::get('steps', function () {
        return view('ui-elements.steps');
    });
    Route::get('session-idle-timeout', function () {
        return view('ui-elements.session-idle-timeout');
    });
    Route::get('session-timeout', function () {
        return view('ui-elements.session-timeout');
    });
    Route::get('bootstrap-ui', function () {
        return view('ui-elements.bootstrap');
    });
    Route::get('starter-page', function () {
        return view('pages.starter-page');
    });
    Route::get('blank', function () {
        return view('pages.blank');
    });
    Route::get('blank', function () {
        return view('pages.blank');
    });
    Route::get('search-result', function () {
        return view('pages.search-result');
    });
    Route::get('custom-scroll', function () {
        return view('pages.custom-scroll');
    });
    Route::get('lock-screen', function () {
        return view('pages.lock-screen');
    });
    Route::get('recoverpw', function () {
        return view('pages.recoverpw');
    });
    Route::get('animation', function () {
        return view('pages.animation');
    });
    Route::get('profile', function () {
        return view('pages.profile');
    });
    Route::get('invoice', function () {
        return view('pages.invoice');
    });
    Route::get('gallery', function () {
        return view('pages.gallery');
    });
    Route::get('pricing', function () {
        return view('pages.pricing');
    });
    Route::get('400', function () {
        return view('pages.400');
    });
    Route::get('403', function () {
        return view('pages.403');
    });
    Route::get('404', function () {
        return view('pages.404');
    });
    Route::get('500', function () {
        return view('pages.500');
    });
    Route::get('503', function () {
        return view('pages.503');
    });
    Route::get('form-basic', function () {
        return view('forms.form-basic');
    });
    Route::get('form-layout', function () {
        return view('forms.form-layout');
    });
    Route::get('icheck-control', function () {
        return view('forms.icheck-control');
    });
    Route::get('form-advanced', function () {
        return view('forms.form-advanced');
    });
    Route::get('form-upload', function () {
        return view('forms.form-upload');
    });
    Route::get('form-dropzone', function () {
        return view('forms.form-dropzone');
    });
    Route::get('form-pickers', function () {
        return view('forms.form-pickers');
    });
    Route::get('basic-table', function () {
        return view('tables.basic-table');
    });
    Route::get('table-layouts', function () {
        return view('tables.table-layouts');
    });
    Route::get('data-table', function () {
        return view('tables.data-table');
    });
    Route::get('bootstrap-tables', function () {
        return view('tables.bootstrap-tables');
    });
    Route::get('responsive-tables', function () {
        return view('tables.responsive-tables');
    });
    Route::get('editable-tables', function () {
        return view('tables.editable-tables');
    });
    Route::get('inbox', function () {
        return view('inbox.inbox');
    });
    Route::get('inbox-detail', function () {
        return view('inbox.inbox-detail');
    });
    Route::get('compose', function () {
        return view('inbox.compose');
    });
    Route::get('contact', function () {
        return view('inbox.contact');
    });
    Route::get('contact-detail', function () {
        return view('inbox.contact-detail');
    });
    Route::get('calendar', function () {
        return view('extra.calendar');
    });
    Route::get('widgets', function () {
        return view('extra.widgets');
    });
    Route::get('morris-chart', function () {
        return view('charts.morris-chart');
    });
    Route::get('peity-chart', function () {
        return view('charts.peity-chart');
    });
    Route::get('knob-chart', function () {
        return view('charts.knob-chart');
    });
    Route::get('sparkline-chart', function () {
        return view('charts.sparkline-chart');
    });
    Route::get('simple-line', function () {
        return view('icons.simple-line');
    });
    Route::get('fontawesome', function () {
        return view('icons.fontawesome');
    });
    Route::get('map-google', function () {
        return view('maps.map-google');
    });
    Route::get('map-vector', function () {
        return view('maps.map-vector');
    });

    #Permission management
    Route::get('permission-management', 'PermissionController@getIndex');
    Route::get('permission/create', 'PermissionController@create');
    Route::post('permission/create', 'PermissionController@save');
    Route::get('permission/delete/{id}', 'PermissionController@delete');
    Route::get('permission/edit/{id}', 'PermissionController@edit');
    Route::post('permission/edit/{id}', 'PermissionController@update');

    #Role management
    Route::get('role-management', 'RoleController@getIndex');
    Route::get('role/create', 'RoleController@create');
    Route::post('role/create', 'RoleController@save');
    Route::get('role/delete/{id}', 'RoleController@delete');
    Route::get('role/edit/{id}', 'RoleController@edit');
    Route::post('role/edit/{id}', 'RoleController@update');

    #CRUD Generator
    Route::get('/crud-generator', ['uses' => 'ProcessController@getGenerator']);
    Route::post('/crud-generator', ['uses' => 'ProcessController@postGenerator']);

    # Activity log
    Route::get('activity-log', 'LogViewerController@getActivityLog');
    Route::get('activity-log/data', 'LogViewerController@activityLogData')->name('activity-log.data');

    # Contact Request Management
    Route::get('contact-request-management', 'AdminController@contactRequestManagement')->name('admin.contact-request-management');
    Route::get('admin-chat-view', 'AdminController@adminChatView')->name('admin.chat.view');
    Route::get('contact-request-view/{id}', 'AdminController@contactRequestView')->name('admin.contact-request.view');
    Route::get('chat-messages/{id}', 'AdminController@chatMessages')->name('admin.chat.messages');
    Route::get('job-details/{id}', 'AdminController@jobDetails')->name('admin.job.details');

});

//Log Viewer
Route::get('log-viewers', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@index')->name('log-viewers');
Route::get('log-viewers/logs', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@listLogs')->name('log-viewers.logs');
Route::delete('log-viewers/logs/delete', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@delete')->name('log-viewers.logs.delete');
Route::get('log-viewers/logs/{date}', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@show')->name('log-viewers.logs.show');
Route::get('log-viewers/logs/{date}/download', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@download')->name('log-viewers.logs.download');
Route::get('log-viewers/logs/{date}/{level}', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@showByLevel')->name('log-viewers.logs.filter');
Route::get('log-viewers/logs/{date}/{level}/search', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@search')->name('log-viewers.logs.search');
Route::get('log-viewers/logcheck', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@logCheck')->name('log-viewers.logcheck');


Route::get('auth/{provider}/', 'Auth\SocialLoginController@redirectToProvider');
Route::get('{provider}/callback', 'Auth\SocialLoginController@handleProviderCallback');
Route::get('logout', 'Auth\LoginController@logout');

// Website Routes
Route::get('email_authentication', 'WebsiteController@emailAuthentication')->name('email_authentication');
Route::get('registration_role', 'WebsiteController@registrationRole')->name('registration_role');
Route::get('finalize_profile', 'WebsiteController@finalizeProfile')->name('finalize_profile');
Route::get('registration_document', 'WebsiteController@registrationDocument')->name('registration_document');
Route::get('reset_password', 'WebsiteController@resetPassword')->name('reset_password');
Route::get('password-template', 'WebsiteController@passwordTemplate')->name('password-template');


Route::get('home', 'WebsiteController@home')->name('home');
Route::get('about_us', 'WebsiteController@aboutUs')->name('about_us');
Route::get('contact', 'WebsiteController@contact')->name('contact');
Route::get('privacy_policy', 'WebsiteController@privacyPolicy')->name('privacy_policy');
Route::get('terms_condition', 'WebsiteController@termsCondition')->name('terms_condition');
Route::get('testing/{id?}', 'WebsiteController@testing')->name('testing');
//register
Route::post('create_user', 'WebsiteController@createUser')->name('create_user');//unaiz
Route::post('upload_users_attachment', 'WebsiteController@uploadUsersAttachment')->name('upload_users_attachment');
Route::post('upload_jobs_attachment', 'WebsiteController@uploadJobsAttachment')->name('upload_jobs_attachment');
Route::get('remove_users_attachment/{image_name?}', 'WebsiteController@removeUsersAttachment')->name('remove_users_attachment');

Auth::routes();
Auth::routes();

Route::resource('commonSetting/common-setting', 'CommonSetting\\CommonSettingController');
Route::get('/clear-all', function () {
    $exitCodeConfig = Artisan::call('storage:link');
    $exitCodeConfig = Artisan::call('route:clear');
    $exitCodeCache = Artisan::call('cache:clear');
    $exitCodeUpdate = Artisan::call('optimize:clear');
    $exitCodeView = Artisan::call('view:clear');
    // $exitCodePermissionCache = Artisan::call('permission:cache-reset');
    //$exitCodePermissionCache = Artisan::call('cache:forget laravelspatie.permission.cache');
    return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
});


Route::resource('contact/contact', 'Contact\\ContactController');
Route::post('contact_us_store','WebsiteController@contactUsStore')->name('contact_us_store');
Route::get('job_complete_cron','AdminController@jobCompleteCron')->name('job_complete_cron');//cron jobs

Route::resource('userGeographicalPreference/user-geographical-preference', 'UserGeographicalPreference\\UserGeographicalPreferenceController');
Route::resource('userRestrictedState/user-restricted-state', 'UserRestrictedState\\UserRestrictedStateController');
Route::resource('portfolioAttachment/portfolio-attachment', 'PortfolioAttachment\\PortfolioAttachmentController');


Route::resource('cmsHomePage/cms-home-page', 'CmsHomePage\\CmsHomePageController');
Route::resource('testimonial/testimonial', 'Testimonial\\TestimonialController');
Route::resource('contactPage/contact-page', 'ContactPage\\ContactPageController');
Route::resource('privacyPolicy/privacy-policy', 'PrivacyPolicy\\PrivacyPolicyController');
Route::resource('termAndCondition/term-and-condition', 'TermAndCondition\\TermAndConditionController');
Route::resource('footerPage/footer-page', 'FooterPage\\FooterPageController');
Route::resource('aboutPage/about-page', 'AboutPage\\AboutPageController');
//-----CmsController----\\
Route::post('home-page-cms','CmsController@homePageCms')->name('home_page_cms');
Route::post('about-page-cms','CmsController@aboutPageCms')->name('about_page_cms');
Route::post('contact-page-cms','CmsController@contactPageCms')->name('contact_page_cms');
Route::post('term-condition-cms','CmsController@termConditionCms')->name('term_condition_cms');
Route::post('privacy-policy-cms','CmsController@privacyPolicyCms')->name('privacy_policy_cms');
Route::post('footer-cms','CmsController@footerCms')->name('footer_cms');
Route::post('/subscribe', 'WebsiteController@subscribe')->name('subscribe');
Route::resource('requestStaffAssign/request-staff-assign', 'RequestStaffAssign\\RequestStaffAssignController');
Route::resource('assignEvent/assign-event', 'AssignEvent\\AssignEventController');
//================================\\

Route::get('login/google/{type}','WebsiteController@redirectToGoogle')->name('redirect_to_google');
Route::get('google_callback','WebsiteController@handleGoogleCallback')->name('google_callback');
//Route::get('/registration-role','WebsiteController@showRoleSelection')->name('registration_role');
Route::post('edit-testimonial/{id?}','WebsiteController@editTestimonial')->name('edit_testimonial');
Route::post('delete-testimonial/{id?}','WebsiteController@deleteTestimonial')->name('delete_testimonial');
//Route::get('/search-sellers', ['WebsiteController@searchSeller'])->name('searchSeller');

//Route::get('threeDayWithoutPosted','AdminController@threeDayWithoutPosted')->name('3days');

Route::resource('assignSellerExpertise/assign-seller-expertise', 'AssignSellerExpertise\\AssignSellerExpertiseController');
Route::resource('estimateOffer/estimate-offer', 'EstimateOffer\\EstimateOfferController');
Route::resource('userCategory/user-category', 'UserCategory\\UserCategoryController');
Route::resource('userCategoryRequest/user-category-request', 'UserCategoryRequest\\UserCategoryRequestController');
Route::resource('userRequest/user-request', 'UserRequest\\UserRequestController');
Route::resource('notification/notification', 'Notification\\NotificationController');

Route::get('/test-preferences', function() {
    $prefs = \App\UserGeographicalPreference::where('user_id', auth()->id())->first();
    if ($prefs) {
        return response()->json([
            'found' => true,
            'data' => $prefs->toArray()
        ]);
    }
    return response()->json(['found' => false]);
});


Route::resource('chat/chat', 'Chat\\ChatController');
Route::resource('chatMessage/chat-message', 'ChatMessage\\ChatMessageController');
Route::resource('chatAttachment/chat-attachment', 'ChatAttachment\\ChatAttachmentController');