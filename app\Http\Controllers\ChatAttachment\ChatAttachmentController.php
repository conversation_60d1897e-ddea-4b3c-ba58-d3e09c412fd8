<?php

namespace App\Http\Controllers\ChatAttachment;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\ChatAttachment;
use Illuminate\Http\Request;

class ChatAttachmentController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('chatattachment','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $chatattachment = ChatAttachment::where('chat_message_id', 'LIKE', "%$keyword%")
                ->orWhere('file_path', 'LIKE', "%$keyword%")
                ->orWhere('file_type', 'LIKE', "%$keyword%")
                ->orWhere('file_size', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $chatattachment = ChatAttachment::paginate($perPage);
            }

            return view('chatAttachment.chat-attachment.index', compact('chatattachment'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('chatattachment','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('chatAttachment.chat-attachment.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('chatattachment','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            ChatAttachment::create($requestData);
            return redirect('chatAttachment/chat-attachment')->with('flash_message', 'ChatAttachment added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('chatattachment','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $chatattachment = ChatAttachment::findOrFail($id);
            return view('chatAttachment.chat-attachment.show', compact('chatattachment'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('chatattachment','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $chatattachment = ChatAttachment::findOrFail($id);
            return view('chatAttachment.chat-attachment.edit', compact('chatattachment'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('chatattachment','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $chatattachment = ChatAttachment::findOrFail($id);
             $chatattachment->update($requestData);

             return redirect('chatAttachment/chat-attachment')->with('flash_message', 'ChatAttachment updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('chatattachment','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            ChatAttachment::destroy($id);

            return redirect('chatAttachment/chat-attachment')->with('flash_message', 'ChatAttachment deleted!');
        }
        return response(view('403'), 403);

    }
}
