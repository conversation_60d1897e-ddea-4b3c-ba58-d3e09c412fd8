
<?php $__env->startPush("css"); ?>
<style>
.contact-request-message {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 20px 0;
    text-align: center;
}

.request-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.seller-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #007bff;
}

.request-content h6 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
}

.request-content p {
    margin: 5px 0;
    color: #666;
}

.request-actions {
    margin-top: 20px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn-accept {
    background-color: #28a745;
    border-color: #28a745;
    padding: 10px 30px;
    font-weight: bold;
}

.btn-reject {
    background-color: #dc3545;
    border-color: #dc3545;
    padding: 10px 30px;
    font-weight: bold;
}

.pending-request {
    background-color: #ffc107 !important;
    color: #212529 !important;
    font-weight: bold;
}

.contact-request .user_msg span p {
    color: #007bff;
    font-weight: 500;
}

.approved-chat .status {
    background-color: #28a745;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div> 
                </div>
            </div>
        </div>
    </section>
    <section class="custom_chats">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="nav nav-pills chats_detail" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <div class="new_chat">
                            <h3>Inbox</h3>
                        </div>
                        <div class="searchbar_input">
                            <input type="search" id="myinput" class="custom_search_box form-control" placeholder="Search">
                        </div>
                        <div class="all_users_chats myTable">
                            <?php
                                // Get contact requests for buyer's jobs
                                $contactRequests = \App\ContactRequest::with(['job', 'seller'])
                                    ->whereHas('job', function($query) {
                                        $query->where('user_id', auth()->user()->id);
                                    })
                                    ->where('status', 'pending')
                                    ->orderBy('created_at', 'desc')
                                    ->get();

                                // Get approved chats
                                $approvedChats = \App\Chat::with(['seller', 'job'])
                                    ->where('buyer_id', auth()->user()->id)
                                    ->where('chat_type', 'contact_request')
                                    ->orderBy('created_at', 'desc')
                                    ->get();
                            ?>

                            
                            <?php $__currentLoopData = $contactRequests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $request): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="user_profile_msg nav-link contact-request <?php echo e($index == 0 ? 'active' : ''); ?>"
                                     id="v-pills-request-tab<?php echo e($request->id); ?>"
                                     data-bs-toggle="pill"
                                     data-bs-target="#v-pills-request<?php echo e($request->id); ?>"
                                     role="tab"
                                     aria-controls="v-pills-request<?php echo e($request->id); ?>"
                                     aria-selected="<?php echo e($index == 0 ? 'true' : 'false'); ?>"
                                     tabindex="-1">
                                    <div class="user_messages">
                                        <div class="user_img">
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png">
                                        </div>
                                        <div class="user_msg">
                                            <h6><?php echo e($request->seller->name ?? 'Unknown'); ?></h6>
                                            <span><p>Contact request for: <?php echo e(Str::limit($request->job->project_title ?? 'Project', 30)); ?></p></span>
                                        </div>
                                    </div>
                                    <div class="time">
                                        <p><?php echo e($request->created_at->format('h:i A')); ?></p>
                                        <span class="status pending-request">!</span>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            
                            <?php $__currentLoopData = $approvedChats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $chat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="user_profile_msg nav-link approved-chat <?php echo e(($contactRequests->count() == 0 && $index == 0) ? 'active' : ''); ?>"
                                     id="v-pills-chat-tab<?php echo e($chat->id); ?>"
                                     data-bs-toggle="pill"
                                     data-bs-target="#v-pills-chat<?php echo e($chat->id); ?>"
                                     role="tab"
                                     aria-controls="v-pills-chat<?php echo e($chat->id); ?>"
                                     aria-selected="<?php echo e(($contactRequests->count() == 0 && $index == 0) ? 'true' : 'false'); ?>"
                                     tabindex="-1">
                                    <div class="user_messages">
                                        <div class="user_img">
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png">
                                        </div>
                                        <div class="user_msg">
                                            <h6><?php echo e($chat->seller->name ?? 'Unknown'); ?></h6>
                                            <span>You: <p>Active chat</p></span>
                                        </div>
                                    </div>
                                    <div class="time">
                                        <p><?php echo e($chat->created_at->format('h:i A')); ?></p>
                                        <span class="status"><?php echo e($chat->messages()->count()); ?></span>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <?php if($contactRequests->count() == 0 && $approvedChats->count() == 0): ?>
                                <div class="text-center py-4">
                                    <p class="text-muted">No messages yet</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="tab-content" id="v-pills-tabContent">
                        
                        <?php $__currentLoopData = $contactRequests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $request): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="tab-pane fade <?php echo e($index == 0 ? 'active show' : ''); ?>"
                                 id="v-pills-request<?php echo e($request->id); ?>"
                                 role="tabpanel"
                                 aria-labelledby="v-pills-request-tab<?php echo e($request->id); ?>">
                                <div class="chats_section">
                                    <div class="users_chats">
                                        <div class="user_profile">
                                            <div class="user_img">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png" alt="User">
                                            </div>
                                            <div class="user_name">
                                                <h5><?php echo e($request->seller->name ?? 'Unknown'); ?></h5>
                                                <p><?php echo e($request->created_at->diffForHumans()); ?></p>
                                            </div>
                                        </div>
                                        <div class="chats_wrapper">
                                            <div class="chat_messages">
                                                <div class="custom_users_chats">
                                                    <div class="messages">
                                                        <div class="contact-request-message">
                                                            <div class="request-info">
                                                                <div class="seller-avatar">
                                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png" alt="Seller">
                                                                </div>
                                                                <div class="request-content">
                                                                    <h6><?php echo e($request->seller->name ?? 'Unknown'); ?> would like to message you</h6>
                                                                    <p><strong>Project:</strong> <?php echo e($request->job->project_title ?? 'N/A'); ?></p>
                                                                    <p><strong>Budget:</strong> $<?php echo e($request->job->budget ?? 'N/A'); ?></p>
                                                                    <div class="request-actions">
                                                                        <button type="button" class="btn btn-success btn-accept"
                                                                                onclick="handleContactRequest(<?php echo e($request->id); ?>, 'approved')">
                                                                            Accept
                                                                        </button>
                                                                        <button type="button" class="btn btn-danger btn-reject"
                                                                                onclick="handleContactRequest(<?php echo e($request->id); ?>, 'rejected')">
                                                                            Reject
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        
                        <?php $__currentLoopData = $approvedChats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $chat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="tab-pane fade <?php echo e(($contactRequests->count() == 0 && $index == 0) ? 'active show' : ''); ?>"
                                 id="v-pills-chat<?php echo e($chat->id); ?>"
                                 role="tabpanel"
                                 aria-labelledby="v-pills-chat-tab<?php echo e($chat->id); ?>">
                                <div class="chats_section">
                                    <div class="users_chats">
                                        <div class="user_profile">
                                            <div class="user_img">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png" alt="User">
                                            </div>
                                            <div class="user_name">
                                                <h5><?php echo e($chat->seller->name ?? 'Unknown'); ?></h5>
                                                <p><?php echo e($chat->created_at->diffForHumans()); ?></p>
                                            </div>
                                            <div class="dispute_btn">
                                                <button type="button" class="btn btn_red" data-bs-toggle="modal" data-bs-target="#seller_chat_dispute">Dispute</button>
                                            </div>
                                        </div>
                                        <div class="chats_wrapper append_input_wrapper">
                                            <div class="chat_messages">
                                                <div class="custom_users_chats">
                                                    <div class="messages">
                                                        <div class="clip received">
                                                            <div class="text">Hello There</div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="text">It is a long established fact that a reader</div>
                                                        </div>
                                                        <div class="profile_owner received">
                                                            <div class="msg-time"> <p>10:11 AM</p></div>
                                                        </div>
                                                        <div class="clip sent">
                                                            <div class="text">Hello There</div>
                                                        </div>
                                                        <div class="clip sent">
                                                            <div class="text">Lorem ipsum is simply a dummy text.
                                                            </div>
                                                        </div>
                                                        <div class="profile_owner sent">
                                                            <div class="msg-time"> <p>10:11 AM</p></div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="text">Hello There</div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="text">It is a long established fact that a reader</div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="upload_file">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/file-pdf-solid.svg">
                                                            </div>
                                                        </div>
                                                        <div class="clip received">
                                                            <div class="text">It is a long established fact that a reader</div>
                                                        </div>
                                                        <div class="profile_owner received">
                                                            <div class="msg-time"> <p>10:11 AM</p></div>
                                                        </div>
                                                        <div class="clip sent">
                                                            <div class="send_img">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/plumbering.png">
                                                            </div>
                                                        </div>
                                                        <div class="clip sent">
                                                            <div class="text">Lorem ipsum is simply a dummy text.
                                                            </div>
                                                        </div>
                                                        <div class="profile_owner sent">
                                                            <div class="msg-time"> <p>10:11 AM</p></div>
                                                        </div>
                                                        <div class="custom_append_msg">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <form>
                                                <div class="send_msg">
                                                    <input type="text" placeholder="Type Here" class="form-control enter_msg">
                                                    <input type="file" class="form-control">
                                                    <button class="btn_blue send_message" type="button"><i class="fa-solid fa-paper-plane"></i></button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <div class="accept_reject_msg">
                                        <div class="user_profile">
                                            <div class="user_img">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/service_img.png">
                                            </div>
                                            <div class="user_name">
                                                <h5>john Doe</h5>
                                            </div>
                                        </div>
                                        <div class="new_message">
                                            <p>John Doe would lke to message you</p>
                                        </div>
                                        <div class="custom_btn">
                                            <button type="button" class="btn btn_red" value="rejected">Reject</button>
                                            <button type="button" class="btn btn_green" value="accepted">Accept</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="modal fade dispute_milestone" id="seller_chat_dispute" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Dispute Chat</h3>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Disputed Milestone</label>
                                    <select class="form-control" aria-label="Default select example">
                                        <option selected disabled>Open this select menu</option>
                                        <option value="1">John Doe</option>
                                        <option value="2">Alyan</option>
                                        <option value="3">imtiaz</option>
                                        <option value="3">Umair</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Reason To Dispute</label>
                                    <textarea placeholder="Enter Comments" rows="5" class="form-control"></textarea>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_btn">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                                    <button type="submit" class="btn btn_black">Submit<span><i class="fa-solid fa-arrow-right"></i></span></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>

<script>
    $(document).ready(function () {
        $('.send_msg button').attr('disabled','disabled');
        $('.send_msg input[type="text"]').keyup(function(){
            if($(this).val().length > 0){
                $('.send_msg button').removeAttr('disabled');
            }
            else if($(this).val().length == 0){
                $('.send_msg button').attr('disabled','disabled');
            }
        });

        $(".send_msg input[type=file]").click(function () {
            $(".send_msg input[type=file]").css("width", "150px");
        });

        $(".users_chats").hide();
        $('.accept_reject_msg button').click(function() {
            var btnValue = $(this).val();
            if (btnValue === "accepted") {
                console.log(btnValue);
                $(this).closest('.chats_section').find('.users_chats').show();
                $(this).closest('.chats_section').find(".accept_reject_msg").hide();
            }
            else if (btnValue === "rejected") {
                $(this).closest('.chats_section').find(".accept_reject_msg .new_message p").html("Message Has Been Rejected");
                $(this).closest('.chats_section').find(".accept_reject_msg .custom_btn").hide();
                console.log(btnValue);
            };
        });
        $(".searchbar_input .custom_search_box").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $(".all_users_chats.myTable *").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
        $(".send_msg button").click(function () {
            $(".custom_chats .chats_section .chat_messages").animate({
                scrollTop: $('.custom_chats .chats_section .chat_messages').get(0).scrollHeight
            }, 2000);
        });
    });

    // Contact Request Handling Function
    window.handleContactRequest = function(requestId, status) {
        if (!confirm('Are you sure you want to ' + status + ' this contact request?')) {
            return;
        }

        $.ajax({
            url: '<?php echo e(route("buyer.contact-request.update", ":id")); ?>'.replace(':id', requestId),
            type: 'PUT',
            data: {
                _token: '<?php echo e(csrf_token()); ?>',
                status: status
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert(response.message);

                    // Reload the page to update the chat interface
                    window.location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Error processing request. Please try again.');
                console.error(xhr.responseText);
            }
        });
    };

</script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mrdoall-git\resources\views/website/buyer/buyer_chat.blade.php ENDPATH**/ ?>