<?php

namespace App\Http\Controllers;

use App\Profile;
use App\User;
use App\Job;
use App\ContactRequest;
use App\Events\UserNotifyEvent;
use Carbon\Carbon;
use Illuminate\Http\Request;

class BuyerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    public function buyerChat(){
        return view("website.buyer.buyer_chat");
    }

    /**
     * Display contact requests for buyer's jobs
     */
    public function contactRequests()
    {
        $contactRequests = ContactRequest::with(['job', 'seller'])
            ->whereHas('job', function($query) {
                $query->where('user_id', auth()->user()->id);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view("website.buyer.contact_requests", compact('contactRequests'));
    }

    /**
     * Approve or reject a contact request
     */
    public function updateContactRequestStatus(Request $request, $id)
    {
        $contactRequest = ContactRequest::with(['job', 'seller'])->findOrFail($id);

        // Check if the job belongs to the authenticated buyer
        if ($contactRequest->job->user_id !== auth()->user()->id) {
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'You are not authorized to manage this contact request.',
                'type' => 'error'
            ]);
        }

        $status = $request->input('status'); // 'approved' or 'rejected'

        if (!in_array($status, ['approved', 'rejected'])) {
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Invalid status provided.',
                'type' => 'error'
            ]);
        }

        $contactRequest->update(['status' => $status]);

        // Send notification to seller
        $data = [
            'title' => 'Contact Request ' . ucfirst($status),
            'message' => 'Your contact request for job "' . $contactRequest->job->project_title . '" has been ' . $status,
            'type' => 'contact_request_' . $status
        ];
        event(new UserNotifyEvent($contactRequest->seller_id, $data['type'], $data));

        // If approved, create a new chat
        if ($status === 'approved') {
            $this->createChatForApprovedRequest($contactRequest);
        }

        return redirect()->back()->with([
            'title' => 'Success',
            'message' => 'Contact request has been ' . $status . ' successfully.',
            'type' => 'success'
        ]);
    }

    /**
     * Create a new chat when contact request is approved
     */
    private function createChatForApprovedRequest($contactRequest)
    {
        // Check if chat already exists
        $existingChat = \App\Chat::where('job_id', $contactRequest->job_id)
            ->where('buyer_id', $contactRequest->job->user_id)
            ->where('seller_id', $contactRequest->seller_id)
            ->where('chat_type', 'contact_request')
            ->first();

        if (!$existingChat) {
            \App\Chat::create([
                'job_id' => $contactRequest->job_id,
                'buyer_id' => $contactRequest->job->user_id,
                'seller_id' => $contactRequest->seller_id,
                'chat_type' => 'contact_request',
                'reference_id' => $contactRequest->id
            ]);
        }
    }
}
