@extends('layouts.master')

@section('content')
    <div class="container-fluid">
        <!-- .row -->
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">{{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'ChatAttachment') }} {{ $chatattachment->id }}</h3>
                    @can('view-'.str_slug('ChatAttachment'))
                        <a class="btn btn-success pull-right" href="{{ url('/chatAttachment/chat-attachment') }}">
                            <i class="icon-arrow-left-circle" aria-hidden="true"></i> Back</a>
                    @endcan
                    <div class="clearfix"></div>
                    <hr>
                    <div class="table-responsive">
                        <table class="table table">
                            <tbody>
                            <tr>
                                <th>ID</th>
                                <td>{{ $chatattachment->id }}</td>
                            </tr>
                            <tr><th> Chat Message Id </th><td> {{ $chatattachment->chat_message_id }} </td></tr><tr><th> File Path </th><td> {{ $chatattachment->file_path }} </td></tr><tr><th> File Type </th><td> {{ $chatattachment->file_type }} </td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

