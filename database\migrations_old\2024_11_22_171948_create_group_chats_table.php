<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateGroupChatsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('group_chats', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->string('name')->nullable();
            $table->string('admin_id')->nullable();
            $table->string('members')->nullable();
            $table->string('group_id')->nullable();
            $table->string('image')->nullable();
            $table->string('sender_id')->nullable();
            $table->string('receiver')->nullable();
            $table->string('show_group')->nullable();
            $table->string('order_by')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('group_chats');
    }
}
