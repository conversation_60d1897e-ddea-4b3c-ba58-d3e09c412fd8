<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessageViewedsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('message_vieweds', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->text('product_id')->nullable();
            $table->text('send_to')->nullable();
            $table->text('receiver')->nullable();
            $table->text('is_group')->nullable();
            $table->text('group_id')->nullable();
            $table->text('viewed')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('message_vieweds');
    }
}
