<div class="form-group {{ $errors->has('chat_message_id') ? 'has-error' : ''}}">
    <label for="chat_message_id" class="col-md-4 control-label">{{ 'Chat Message Id' }}</label>
    <div class="col-md-6">
        <input class="form-control" name="chat_message_id" type="text" id="chat_message_id" value="{{ $chatattachment->chat_message_id??''}}" >
        {!! $errors->first('chat_message_id', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('file_path') ? 'has-error' : ''}}">
    <label for="file_path" class="col-md-4 control-label">{{ 'File Path' }}</label>
    <div class="col-md-6">
        <input class="form-control" name="file_path" type="text" id="file_path" value="{{ $chatattachment->file_path??''}}" >
        {!! $errors->first('file_path', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('file_type') ? 'has-error' : ''}}">
    <label for="file_type" class="col-md-4 control-label">{{ 'File Type' }}</label>
    <div class="col-md-6">
        <input class="form-control" name="file_type" type="text" id="file_type" value="{{ $chatattachment->file_type??''}}" >
        {!! $errors->first('file_type', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('file_size') ? 'has-error' : ''}}">
    <label for="file_size" class="col-md-4 control-label">{{ 'File Size' }}</label>
    <div class="col-md-6">
        <input class="form-control" name="file_size" type="text" id="file_size" value="{{ $chatattachment->file_size??''}}" >
        {!! $errors->first('file_size', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        <input class="btn btn-primary" type="submit" value="{{ $submitButtonText??'Create' }}">
    </div>
</div>
