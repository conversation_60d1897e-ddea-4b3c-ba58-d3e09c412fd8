<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateFooterPagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('footer_pages', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->text('section_one_image')->nullable();
            $table->text('section_one_heading')->nullable();
            $table->text('section_two_description')->nullable();
            $table->text('section_two_heading_one')->nullable();
            $table->text('section_two_heading_two')->nullable();
            $table->text('section_two_description_one')->nullable();
            $table->text('section_two_description_two')->nullable();
            $table->text('section_two_description_three')->nullable();
            $table->text('section_two_description_four')->nullable();
            $table->text('section_two_description_five')->nullable();
            $table->text('section_two_description_six')->nullable();
            $table->text('section_two_fb')->nullable();
            $table->text('section_two_instagram')->nullable();
            $table->text('section_two_twitter')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('footer_pages');
    }
}
