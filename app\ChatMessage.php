<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatMessage extends Model
{
    use SoftDeletes;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'chat_messages';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['chat_id', 'sender_id', 'message', 'attachment_path', 'attachment_type'];

    public function chat() {
        return $this->belongsTo(Chat::class, 'chat_id');
    }

    public function sender() {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function attachments() {
        return $this->hasMany(ChatAttachment::class, 'chat_message_id');
    }

}
