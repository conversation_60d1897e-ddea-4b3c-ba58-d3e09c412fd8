
<?php $__env->startPush("css"); ?>
    <style>
        header,footer{display: none;}
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>


























































<section id="" class="login_register">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-7 col-md-6 custom_column_padding">
                <div class="custom_banner">
                    <video autoplay muted loop playsinline webkit-playsinline>
                        <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                    </video>
                    <div class="background_banner">
                        <div class="banner_content custom_login_title">
                            <a href="<?php echo e(url('home')); ?>">
                                <div class="site_logo">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png">
                                </div>
                            </a>
                            <div class="site_title">
                                <h1>Tackle any home improvement project, effortlessly</h1>
                            </div>
                            <div class="site_key_parameter">
                                <a href="javascript:void(0)"><i class="fa-regular fa-circle-check"></i>Free consultation</a>
                                <a href="javascript:void(0)"><i class="fa-solid fa-award"></i>Satisfaction Guaranteed</a>
                                <a href="javascript:void(0)"><img src="<?php echo e(asset('website')); ?>/assets/images/banner_sheild.png">Protected Payments</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-5 col-md-6 custom_column_padding">
                <div class="custom_scrollbar">
                    <div class="login_box">
                        <form class="form-horizontal form-material" id="loginform" method="post" action="<?php echo e(route('login')); ?>">
                            <?php echo e(csrf_field()); ?>

                            <?php if(session('error')): ?>
                                <div class="alert alert-danger">
                                    <?php echo e(session('error')); ?>

                                </div>
                            <?php endif; ?>

                            <div class="row custom_row">
                                <div class="col-md-12"><h1>Sign In</h1></div>
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Email</label>
                                        <input id="email" type="email" autocomplete="username" placeholder="Enter Email" class="form-control<?php echo e($errors->has('email') ? ' is-invalid' : ''); ?>" name="email" value="<?php echo e(old('email')); ?>" required autofocus>
                                        <?php if($errors->has('email')): ?>
                                            <span class="invalid-feedback">
                                            <strong><?php echo e($errors->first('email')); ?></strong>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Password</label>
                                        <input id="password"  type="password" autocomplete="new-password" class="form-control<?php echo e($errors->has('password') ? ' is-invalid' : ''); ?> password_eye" name="password" required placeholder="Enter Password">
                                        <?php if($errors->has('password')): ?>
                                            <span class="invalid-feedback">
                                                    <strong><?php echo e($errors->first('password')); ?></strong>
                                                </span>
                                        <?php endif; ?>
                                        <i class="fa-solid custom_eye_icon fa-eye"></i>
                                        <i class="fa-solid custom_eye_icon fa-eye-slash"></i>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field custom_checkbox">
                                        <div class="checkbox checkbox-primary form-group">
                                            <input type="checkbox" id="checkbox-signup" name="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                                            <label for="checkbox-signup"> Remember Me</label>
                                        </div>
                                        <a href="<?php echo e(route('password.request')); ?>" id="to-recover" class="">Forgot password?</a>
                                    </div>
                                </div>
                                <?php if(env('APP_URL') != 'http://localhost:8000'): ?>

                                <div class="col-md-12">

                                    <div class="g-recaptcha" data-sitekey="<?php echo e(env('RECAPTCHA_SITE_KEY')); ?>"></div>
                                    <span class="text-danger" id="recaptcha-error"></span>
                                </div>
                                <?php endif; ?>
                                <div class="col-md-12">
                                    <div class="submit_btn">
                                        <button class="btn btn_black" type="submit">Sign In
                                            <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <h5>Don't have an account? <a href="<?php echo e(url('registration_role')); ?>" class=""><b>Sign Up</b></a></h5>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="custom_underline">
                                        <h5>Or</h5>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="social">
                                        <a href="<?php echo e(route('redirect_to_google', 'register')); ?>" class="btn btn_white">
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/Group **********.svg" alt="">Sign up with Google
                                        </a>




                                    </div>
                                </div>
                            </div>
                            
                            
                            
                            
                            
                            
                            
                            
                            
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>




<script>
$(document).ready(function() {
        $("#loginform").submit(function(event) {
            const form = this;
            var recaptcha = grecaptcha.getResponse();
            if (recaptcha.length === 0) {
                event.preventDefault();
                $("#recaptcha-error").text("Please verify that you are not a robot.");
                return false;
            } else {
                $("#recaptcha-error").text("");
            $(form).find('button[type="submit"]').prop('disabled', true).text('Signing In...');
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mrdoall-git\resources\views/auth/login.blade.php ENDPATH**/ ?>