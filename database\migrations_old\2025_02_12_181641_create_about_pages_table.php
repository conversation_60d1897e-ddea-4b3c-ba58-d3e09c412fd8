<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAboutPagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('about_pages', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->text('section_one_image')->nullable();
            $table->text('section_one_heading')->nullable();
            $table->text('section_two_image_one')->nullable();
            $table->text('section_two_image_two')->nullable();
            $table->text('section_two_heading')->nullable();
            $table->text('section_two_description')->nullable();
            $table->text('section_three_heading')->nullable();
            $table->text('section_three_sub_one_heading')->nullable();
            $table->text('section_three_sub_one_description_one')->nullable();
            $table->text('section_three_sub_one_description_two')->nullable();
            $table->text('section_three_sub_one_description_three')->nullable();
            $table->text('section_three_sub_one_description_four')->nullable();
            $table->text('section_three_sub_two_heading')->nullable();
            $table->text('section_three_sub_two_description_one')->nullable();
            $table->text('section_three_sub_two_description_two')->nullable();
            $table->text('section_three_sub_two_description_three')->nullable();
            $table->text('section_three_sub_two_description_four')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('about_pages');
    }
}
