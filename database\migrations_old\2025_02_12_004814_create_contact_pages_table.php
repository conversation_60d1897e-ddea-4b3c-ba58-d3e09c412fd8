<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateContactPagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contact_pages', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->text('section_one_image')->nullable();
            $table->text('section_one_heading')->nullable();
            $table->text('section_two_heading')->nullable();
            $table->text('section_two_description')->nullable();
            $table->text('section_two_sub_one_heading')->nullable();
            $table->text('section_two_sub_one_description')->nullable();
            $table->text('section_two_sub_two_heading')->nullable();
            $table->text('section_two_sub_two_description')->nullable();
            $table->text('section_two_sub_three_heading')->nullable();
            $table->text('section_two_sub_three_description')->nullable();
            $table->text('section_three_sub_one_heading')->nullable();
            $table->text('section_three_sub_one_description')->nullable();
            $table->text('section_three_sub_two_heading')->nullable();
            $table->text('section_three_sub_two_description')->nullable();
            $table->text('section_three_sub_three_heading')->nullable();
            $table->text('section_three_sub_three_description')->nullable();
            $table->text('section_three_sub_four_heading')->nullable();
            $table->text('section_three_sub_four_description')->nullable();
            $table->text('section_four_heading')->nullable();
            $table->text('section_four_facebook')->nullable();
            $table->text('section_four_twitter')->nullable();
            $table->text('section_four_linkedin')->nullable();
            $table->text('section_four_google')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('contact_pages');
    }
}
