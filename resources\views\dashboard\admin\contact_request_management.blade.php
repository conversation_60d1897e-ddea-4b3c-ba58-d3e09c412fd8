@extends('dashboard.layout.master')
@push("css")
<style>
.status-badge {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}
</style>
@endpush

@section('content')
<section class="projects_wrapper pagination_scroll_tbl">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="custom_projects_tabs scrollable_tbl">
                    <div class="custom_search_filter custom_flex">
                        <h3>Contact Request Management</h3>
                        <div class="txt_field custom_search">
                            <input type="text" placeholder="Search" class="custom_search_box">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <div class="custom_table">
                            <table id="" class="table table-striped myTable datatable">
                                <thead>
                                <tr>
                                    <th>Seller</th>
                                    <th>Buyer</th>
                                    <th>Job</th>
                                    <th>Status</th>
                                    <th>Request Date</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                @forelse($contactRequests as $request)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ asset('website/assets/images/service_img.png') }}" 
                                                     alt="Seller" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                                                <div>
                                                    <strong>{{ $request->seller->name ?? 'N/A' }}</strong><br>
                                                    <small class="text-muted">{{ $request->seller->email ?? 'N/A' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ asset('website/assets/images/service_img.png') }}" 
                                                     alt="Buyer" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                                                <div>
                                                    <strong>{{ $request->job->user->name ?? 'N/A' }}</strong><br>
                                                    <small class="text-muted">{{ $request->job->user->email ?? 'N/A' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $request->job->project_title ?? 'N/A' }}</strong><br>
                                                <small class="text-muted">{{ $request->job->project_number ?? 'N/A' }}</small><br>
                                                <small class="text-success">${{ $request->job->budget ?? 'N/A' }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="status-badge status-{{ $request->status }}">
                                                {{ ucfirst($request->status) }}
                                            </span>
                                        </td>
                                        <td>{{ $request->created_at->format('d M Y, h:i A') }}</td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn" type="button" id="dropdown_action" data-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdown_action">
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="viewContactRequest({{ $request->id }})">
                                                            <i class="fa-solid fa-eye"></i> View Details
                                                        </a>
                                                    </li>
                                                    @if($request->status === 'approved')
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.chat.view') }}">
                                                            <i class="fa-solid fa-comments"></i> View Chat
                                                        </a>
                                                    </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">No contact requests found</td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3">
                        {{ $contactRequests->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Request Details Modal -->
<div class="modal fade" id="contactRequestModal" tabindex="-1" role="dialog" aria-labelledby="contactRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contactRequestModalLabel">Contact Request Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="contactRequestDetails">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
function viewContactRequest(requestId) {
    $.ajax({
        url: '{{ url("admin/contact-request-view") }}/' + requestId,
        type: 'GET',
        success: function(response) {
            $('#contactRequestDetails').html(response);
            $('#contactRequestModal').modal('show');
        },
        error: function() {
            alert('Error loading contact request details');
        }
    });
}

// Search functionality
$(document).ready(function() {
    $('.custom_search_box').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.myTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
});
</script>
@endpush
