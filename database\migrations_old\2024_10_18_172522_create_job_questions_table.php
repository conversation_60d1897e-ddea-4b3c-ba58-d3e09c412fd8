<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateJobQuestionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('job_questions', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->text('question')->nullable();
            $table->string('question_type')->nullable();
            $table->text('options')->nullable();
            $table->string('image_input')->nullable();
            $table->string('other_input')->nullable();
            $table->string('visit_req')->nullable();
            $table->string('job_category_id')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('job_questions');
    }
}
