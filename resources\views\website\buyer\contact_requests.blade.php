@extends('website.layout.master')
@push("css")
<style>
.contact-request-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-badge {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.btn-approve {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 5px;
    margin-right: 10px;
}

.btn-reject {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 5px;
}

.seller-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.seller-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
}

.job-title {
    color: #007bff;
    font-weight: bold;
    margin-bottom: 10px;
}
</style>
@endpush

@section('content')
<section class="hero_section header_video">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="custom_banner custom_height">
                    <video autoplay muted loop>
                        <source src="{{ asset('website') }}/assets/images/website_video.mp4" type="video/mp4">
                    </video>
                    <div class="background_banner">
                    </div>
                </div> 
            </div>
        </div>
    </div>
</section>

<section class="custom_projects">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="custom_projects_tabs">
                    <h2>Contact Requests</h2>
                    <p>Manage contact requests from service providers for your projects</p>
                    
                    @if($contactRequests->count() > 0)
                        @foreach($contactRequests as $request)
                        <div class="contact-request-card">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="job-title">
                                        {{ $request->job->project_title ?? 'N/A' }}
                                    </div>
                                    
                                    <div class="seller-info">
                                        <img src="{{ asset('website/assets/images/service_img.png') }}" 
                                             alt="Seller" class="seller-avatar">
                                        <div>
                                            <h6>{{ $request->seller->name ?? 'N/A' }}</h6>
                                            <small class="text-muted">{{ $request->seller->email ?? 'N/A' }}</small>
                                        </div>
                                    </div>
                                    
                                    <div class="request-details">
                                        <p><strong>Project Number:</strong> {{ $request->job->project_number ?? 'N/A' }}</p>
                                        <p><strong>Request Date:</strong> {{ $request->created_at->format('d M Y, h:i A') }}</p>
                                        <p><strong>Project Budget:</strong> ${{ $request->job->budget ?? 'N/A' }}</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 text-right">
                                    <div class="mb-3">
                                        <span class="status-badge status-{{ $request->status }}">
                                            {{ ucfirst($request->status) }}
                                        </span>
                                    </div>
                                    
                                    @if($request->status === 'pending')
                                    <div class="action-buttons">
                                        <form method="POST" action="{{ route('buyer.contact-request.update', $request->id) }}" style="display: inline;">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="approved">
                                            <button type="submit" class="btn-approve" onclick="return confirm('Are you sure you want to approve this contact request?')">
                                                Approve
                                            </button>
                                        </form>
                                        
                                        <form method="POST" action="{{ route('buyer.contact-request.update', $request->id) }}" style="display: inline;">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="rejected">
                                            <button type="submit" class="btn-reject" onclick="return confirm('Are you sure you want to reject this contact request?')">
                                                Reject
                                            </button>
                                        </form>
                                    </div>
                                    @elseif($request->status === 'approved')
                                    <div class="mt-2">
                                        <a href="{{ route('buyer-chat') }}" class="btn btn-primary btn-sm">
                                            Go to Chat
                                        </a>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $contactRequests->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <h4>No Contact Requests</h4>
                            <p>You haven't received any contact requests yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('js')
<script>
// Add any JavaScript functionality here if needed
$(document).ready(function() {
    // Auto-hide success/error messages after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
});
</script>
@endpush
