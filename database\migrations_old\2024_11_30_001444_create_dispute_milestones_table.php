<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateDisputeMilestonesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('dispute_milestones', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->string('dispute_title')->nullable();
            $table->string('disputed_milestone_id')->nullable();
            $table->string('enter_message')->nullable();
            $table->string('outcome')->nullable();
            $table->string('job_id')->nullable();
            $table->string('job_offer_id')->nullable();
            $table->string('status')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('dispute_milestones');
    }
}
