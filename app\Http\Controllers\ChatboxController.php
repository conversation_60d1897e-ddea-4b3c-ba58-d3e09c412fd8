<?php

namespace App\Http\Controllers;

use App\EstimateOffer;

use App\Events\UserNotifyEvent;
use App\Notification;
use App\Repositories\NotificationRepositoryInterface;
use App\User;
use App\Message;
use App\GroupChat;
use App\Permission;
use App\Item;
use App\MessageViewed;
use App\UserNotification;
use App\DisputeMilestone;
use App\JobMilestone;
use App\SellerRating;
use App\Job;
use App\JobOffer;
//use Mail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Response;
use Session;
use Storage;
class ChatboxController extends Controller
{
    protected $notificationRepo;
    public function __construct(NotificationRepositoryInterface $notificationRepo)
    {
        $this->middleware('auth');
        $this->notificationRepo = $notificationRepo;
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */

    public function index($ids = "")
    {
        $manager = [];
        $groups = [];
        $company = [];
        $task_manager = [];
        $users = [];
        $milestones_id = [];
//        if(auth()->user()->hasRole('buyer') || auth()->user()->hasRole('seller')){
//        return    $job = Job::where('assign_seller_id', Auth::user()->id)->orWhere('user_id', Auth::user()->id)->first();
//        }

        if (Auth::user()->hasRole('buyer')) {
                $groups = GroupChat::where(function($query)  {
                      $query->where('sender_id', Auth::user()->id);
            })->orWhere(function ($query) use ($milestones_id) {
                      $query->where('receiver', Auth::user()->id);
            })->orderBy('order_by', 'DESC')->get();
        } elseif (Auth::user()->hasRole('seller')) {
                $groups = GroupChat::where('status',1)->where(function($query) {
                      $query->where('sender_id', Auth::user()->id);
                })->orWhere(function ($query) use ($milestones_id) {
                      $query->where('receiver', Auth::user()->id);
                })->orderBy('order_by', 'DESC')->get();
        } elseif (Auth::user()->hasRole('user')) {
                $groups = GroupChat::where('id',$ids)->get();
        }
        return view('website.buyer.buyer_chat', compact('users', 'groups', 'manager', 'company', 'task_manager'));
    }


    public function getMessageProcess(Request $request)
    {

        Session::put('item_id',$request->item);
        Session::put('group_id_actived',$request->user_id);
        $from_user                  = Auth::user()->id;
        $to_user                    = $request->user_id;
        $group                      = $request->group;
//        if(auth()->user()->hasRole('buyer') || auth()->user()->hasRole('seller')) {
//          return  $item = Job::where('id', $request->item)->first();
//            if ($item) {
//                Session::put('task_id', $item->id);
//            }
//        }
        if($group == 1){

            $message_array              = Message::where(function($query) use ($request) {
                                            $query->where('from_user_id',Auth::user()->id )->where('to_user_id',$request->user_id);
                                        })->orWhere(function ($query) use ($request) {
                                            $query->where('to_user_id',Auth::user()->id)->where('from_user_id',$request->user_id);
                                        })->orderBy('created_at', 'ASC')->get();
            MessageViewed::where('group_id',Auth::user()->id)->where('receiver',Auth::user()->id)->where('is_group',1)->update(['viewed'=>'1']);
        }else{

            $message_array              = Message::where(function($query) use ($request) {
                                            $query->where('from_user_id', Auth::user()->id)->where('to_user_id', $request->user_id);
                                        })->orWhere(function ($query) use ($request) {
                                            $query->where('to_user_id', $request->user_id)->where('from_user_id', '!=', Auth::user()->id);
                                        })->orderBy('created_at', 'ASC')->get();
            MessageViewed::where('group_id',$request->user_id)->where('receiver',Auth::user()->id)->where('is_group',1)->update(['viewed'=>'1']);
        }
            $message                    = (isset($message_array) || !empty($message_array)) ? $message_array : 'Falsey Value';
            $response                   = array(
                                        'message'               => $message,
                                        'to_user'               => $to_user,
                                        'from_user'             => $from_user,
                                        'message'               => $message
                                    );
        return view('website.ajax.get_all_users',compact('response'));
    }
    public function sendMessageProcess(Request $request)
    {
        $requestData['product_id']      = $request->item;
        $requestData['group_mes']       = $request->group;
        $requestData['content']         = $request->message;
        $requestData['to_user_id']      = $request->to_user_id;
        $requestData['from_user_id']    = Auth::user()->id;
        $requestData['viewed_by_receiver']    = 0;
        $latest =   Message::where('product_id',$request->item)->where('group_mes',$request->group)->where('to_user_id',$request->to_user_id)->where('from_user_id',Auth::user()->id)->latest()->first();
        $messages                       = Message::create($requestData);
        if($request->group == 0){

            // $item =Item::where('id',$request->item)->first();
            $groupteam = GroupChat::where('group_id',$request->to_user_id)->first();
            GroupChat::where('group_id',$request->to_user_id)->update(['order_by'=>time()]);
            $members = json_decode($groupteam->members);
            foreach ($members as $key => $value) {
               if($value != Auth::id()){
                $user = User::where('id',$value)->first();
                 MessageViewed::create(['product_id'=>Session::get('task_id'),'send_to'=>Auth::user()->id,'receiver'=>$value,'is_group'=>1,'viewed'=>'0','group_id'=>$request->to_user_id]);
                }
            }
        }else{
                MessageViewed::create(['product_id'=>$request->item,'send_to'=>Auth::user()->id,'receiver'=>$request->to_user_id,'is_group'=>1,'viewed'=>'0','group_id'=>$request->to_user_id]);
                // UserNotification::create(['sender_id'=>Auth::id(),'receiver_id'=>$request->to_user_id,'type'=>'message','description'=>'Nouveau message de','redirect_url'=>'customer_chat','viewed_by_receiver'=>0,'row_id'=>Auth::id()]);
        }
    }
    public function requestContact(Request $request)
    {
        $id = $request->id;

        $adminUser = User::find(2);
        $item = Job::find($id);
        $userIds = ['2', Auth::id(), $item->user_id];

        $group = GroupChat::where(function ($query) use ($request) {
            $query->where('sender_id', Auth::id())
                ->where('admin_id', $request->id);
        })->orWhere(function ($query) use ($request) {
            $query->where('receiver', Auth::id())
                ->where('admin_id', $request->id);
        })->orderBy('order_by', 'DESC')->first();

        if ($group) {
            GroupChat::where('id', $group->id)->update(['status' => 0]);
            Message::where('to_user_id', $group->group_id)->update(['status' => 0]);
        } else {
            $group = GroupChat::updateOrCreate(
                [
                    'admin_id' => $item->id,
                    'sender_id' => Auth::id(),
                    'receiver' => $item->user_id,
                ],
                [
                    'members' => json_encode($userIds),
                    'sender_id' => Auth::id(),
                    'receiver' => $item->user_id,
                    'order_by' => time(),
                ]
            );

            $groupId = '1099022' . $group->id;
            GroupChat::where('id', $group->id)->update(['group_id' => $groupId]);

            Session::put('item_id', $item->id);
            Session::put('group_id', $groupId);

            $location = Auth::user()->profile;
            $content = '<div class="location_box ">';
            $content .= '<span><i class="fa-solid fa-location-dot"></i></span>';
            $content .= '<p>' . $location->city . ', ' . $location->state . ', ' . $location->country . '</p>';
            $content .= '</div>';

            $requestData = [
                'product_id'         => $id,
                'content'            => $content,
                'to_user_id'         => $groupId,
                'from_user_id'       => Auth::id(),
                'viewed_by_receiver' => 0,
            ];

            Message::create($requestData);

            $groupTeam = GroupChat::where('group_id', $groupId)->first();
            $members = json_decode($groupTeam->members);

            foreach ($members as $memberId) {
                if ($memberId != Auth::id()) {
                    MessageViewed::create([
                        'product_id' => $id,
                        'send_to'    => Auth::id(),
                        'receiver'   => $memberId,
                        'is_group'   => 1,
                        'viewed'     => '0',
                        'group_id'   => $groupId,
                    ]);
                }
            }
        }

        $data = [
            'title' => 'Contact Request',
            'type'  => 'contact_request',
        ];

        event(new UserNotifyEvent($adminUser->id, $data['type'], $data));
        event(new UserNotifyEvent(Auth::id(), $data['type'], $data));
        return redirect()->back()->with([
            'title'   => 'Done',
            'message' => 'Your contact request has been sent successfully. We will get back to you shortly!',
            'type'    => 'success',
        ]);
    }
    public function uplaod_image(Request $request)
    {
        if ($request->hasFile('uploadFile')) {
             $image1=Storage::disk('website')->put('uploadFile',$request->uploadFile??'');
               $type = explode('.',$image1);
              if($type[1] == 'pptx' || $type[1]=='pdf' || $type[1]=='doc' || $type[1]=='docx'){
                return  '<a href="'.asset('website').'/'.$image1.'" download > Download '.$type[1].' File </a></br>';
              }else{
                return '<p>
                <a data-fancybox="gallery" class="fancybox1" href="'.asset('website').'/'.$image1.'"><img src="'.asset('website').'/'.$image1.'" width="20%" height="20%" frameborder="0"/></a></p></br>';
              }

        }
    }
    public function EstimateBidAccept($estimate_id)
    {
        $estimateOffer =  EstimateOffer::find($estimate_id);
        $id = $estimateOffer->job_id;
        $image = '';
        $content = '';
        $item =Job::where('id',$id)->first();
        if (Auth::user()->hasRole('buyer')){
            $sender_id = Auth::id();
            $senderName = $sender->name ?? '';
            $receiver  = $estimateOffer->seller_id;
            $buyerData = [
                'title' => 'Estimate Accepted',
                'message' => "Your estimate has been accepted by the {$senderName}.",
                'type' => 'estimate_accepted',
                'created_at' => now(),
            ];
        }elseif(Auth::user()->hasRole('seller')){
            $sender_id = Auth::id();
            $receiver  = $item->user_id;

        }
        $user = ['2',$sender_id,$receiver];

        $groups = GroupChat::where(function ($query) use ($id) {
            $query->where('sender_id', Auth::user()->id)->where('admin_id',$id);
        })->orWhere(function ($query) use ($id) {
            $query->where('receiver', Auth::user()->id)->where('admin_id',$id);
        })->orderBy('order_by', 'DESC')->first();
        if (isset($groups->id)) {
            GroupChat::where('id',$groups->id)->update(['status'=>1]);
            Message::where('to_user_id',$groups->group_id)->update(['status'=>1]);
            Session::put('group_id','1099022'.$groups->id.'');
        }else{
            $group =  GroupChat::updateOrCreate(['admin_id'=>$item->id,'sender_id'=>$sender_id,'receiver'=>$receiver],['members'=>json_encode($user),'sender_id'=>$sender_id,'receiver'=>$receiver,'order_by'=>time(),'status'=>1]);
            GroupChat::where('id',$group->id)->update(['group_id'=>'1099022'.$group->id.'']);
            Session::put('item_id',$item->id);
            Session::put('group_id','1099022'.$group->id.'');
            $requestData['product_id']      = $id;
            $content .=    '<div class="location_box ">';
            $content .=    '<span><i class="fa-solid fa-location-dot"></i></span>';
            $content .=    '<p>'.Auth::user()->profile->city.','.Auth::user()->profile->state.','.Auth::user()->profile->country.'</p>';
            $content .=    '</div>';
            $requestData['content']         = $content;
            $requestData['to_user_id']      = '1099022'.$group->id.'';
            $requestData['from_user_id']    = Auth::user()->id;
            $requestData['viewed_by_receiver']    = 0;
            $messages  = Message::create($requestData);
            $groupteam = GroupChat::where('group_id','1099022'.$group->id.'')->first();
            $members   = json_decode($groupteam->members);
            foreach ($members as $key => $value) {
                if($value != Auth::id()){
                    $user = User::where('id',$value)->first();
                    MessageViewed::create(['product_id'=>$id,'send_to'=>Auth::user()->id,'receiver'=>$value,'is_group'=>1,'viewed'=>'0','group_id'=>'1099022'.$group->id.'']);
                }
            }
        }
        event(new UserNotifyEvent($receiver, $buyerData['type'], $buyerData));

        return redirect(url('buyer_chat'))->with(['title'=>'Done','message'=>"Your chat successfully created!",'type'=>'success']);;
    }
    public function acceptMessage(Request $request){
      $message = Message::find($request->message_id);
      Message::where('id',$request->message_id)->update(['status'=>$request->status]);
      GroupChat::where('group_id',$message->to_user_id)->update(['status'=>$request->status]);
    }
    public function createAdmin(Request $request)
    {
        $requestData['product_id']      = 0;
        $requestData['group_mes']       = $request->group;
        $requestData['content']         = $request->message;
        $requestData['to_user_id']      = 2;
        $requestData['from_user_id']    = Auth::user()->id;
        $requestData['viewed_by_receiver']    = 0;
        $latest =   Message::where('product_id',0)->where('group_mes',$request->group)->where('to_user_id',$request->to_user_id)->where('from_user_id',Auth::user()->id)->latest()->first();
        $messages = Message::create($requestData);
        MessageViewed::create(['product_id'=>0,'send_to'=>Auth::user()->id,'receiver'=>2,'is_group'=>1,'viewed'=>'0','group_id'=>2]);
        // UserNotification::create(['sender_id'=>Auth::id(),'receiver_id'=>$request->to_user_id,'type'=>'message','description'=>'Nouveau message de','redirect_url'=>'customer_chat','viewed_by_receiver'=>0,'row_id'=>Auth::id()]);
        Session::put('group_id',2);
        return redirect('chat/admin');

    }


    public function disputeMilestoneChat(Request $request){
        extract($request->all());
        $manager       = [];
        $groups        = [];
        $company       = [];
        $task_manager  = [];
        $users         = [];

        if(isset($request->id)){
            Session::put('group_id',$request->id);
        }
        $milestones_id = JobMilestone::where('job_id',$job_id)->pluck('id')->toArray();
        if (Auth::user()->hasRole('buyer')){
            $groups = GroupChat::where(function($query) use ($milestones_id) {
                $query->where('sender_id', Auth::user()->id)->whereIn('milestone_id',$milestones_id);
            })->orWhere(function ($query) use ($milestones_id) {
                $query->where('receiver', Auth::user()->id)->whereIn('milestone_id',$milestones_id);
            })->orderBy('order_by', 'DESC')->get();
        }elseif(Auth::user()->hasRole('seller')){
            $groups = GroupChat::where('status',1)->where(function($query) use ($milestones_id) {
                $query->where('sender_id', Auth::user()->id)->whereIn('milestone_id',$milestones_id);
            })->orWhere(function ($query) use ($milestones_id) {
                $query->where('receiver', Auth::user()->id)->whereIn('milestone_id',$milestones_id);
            })->orderBy('order_by', 'DESC')->get();
        }
        return view('website.ajax.dispute_milestone_chat',compact('users','groups','manager','company','task_manager','job_id'));
    }
      public function resolveDispute(Request $request){
           DisputeMilestone::where('disputed_milestone_id',$request->id)->update(['status'=>1]);
           return response()->json(['title'=>'Done','message' => 'resolved Dispute successfully','type'=>'success']);
      }

    public function decisionDisputeStaff(Request $request){
//        return $request->all();
        $disputeMilestone = DisputeMilestone::where('disputed_milestone_id',$request->resolve_milestone_id)->update(['status'=>1,'decision'=>$request->decision]);
        GroupChat::where('milestone_id',$request->resolve_milestone_id)->update(['dispute_status'=>1,'decision'=>$request->decision]);
        $data = [
            'title'   => 'Dispute Resolved',
            'message' => "The dispute has been resolved by the  with the decision: " . $request->decision,
            'type'    => 'success'
        ];
//        event(new UserNotifyEvent())
        return redirect()->back()->with(['title'=>'Done','message' => 'resolved Dispute successfully','type'=>'success']);
    }//ends function myProjectsOngoingView

    public function resolveDisputeByUser(Request $request){
      if (Auth::user()->hasRole('buyer')) {
            DisputeMilestone::where('disputed_milestone_id',$request->milestone_id)->update(['solve_by_buyer'=>1]);
      }elseif(Auth::user()->hasRole('seller')){
            DisputeMilestone::where('disputed_milestone_id',$request->milestone_id)->update(['solve_by_seller'=>1]);
      }
      $disputed =  DisputeMilestone::where('disputed_milestone_id',$request->milestone_id)->first();
      if ($disputed->solve_by_buyer ==1 && $disputed->solve_by_seller == 1) {
          DisputeMilestone::where('disputed_milestone_id',$request->milestone_id)->update(['status'=>1,'decision'=>'Resolved']);
          GroupChat::where('milestone_id',$request->milestone_id)->update(['dispute_status'=>1,'decision'=>'Resolved','self_resolved'=>'1']);
      }
      return response()->json(['title'=>'Done','message' => 'resolved Dispute successfully','type'=>'success']);
    }//ends function myProjectsOngoingView
  public function escalateToStaffJob(Request $request){
       extract($request->all());
      if(!isset($staff_id) || empty($staff_id) || $staff_id == 0) {
          return response()->json(['title'=>'error','message' => 'Staff is not assigned to this job. Please request the admin to assign a staff member.','type'=>'error']);
      }
       $group =  GroupChat::where('group_id',$user_id)->first();
       $members   = json_decode($group->members);
       array_push($members,$staff_id);
       GroupChat::where('id',$group->id)->update(['members'=>json_encode($members),'show_group'=>1]);

         $content = '';
                        $requestData['product_id']      = $group->admin_id;
                        $content .=    '<div class="location_box ">';
                        // $content .=    '<span><i class="fa-solid fa-location-dot"></i></span>';
                        $content .=    '<p> staff has been assigned</p>';
                        $content .=    '</div>';
                        $requestData['content']         = $content;
                        $requestData['to_user_id']      = '1099022'.$group->id.'';
                        $requestData['from_user_id']    = 2;
                        $requestData['viewed_by_receiver']    = 0;
                        $messages      = Message::create($requestData);
//                        $groupteam = GroupChat::where('group_id','1099022'.$value->id.'')->first();
                        $groupteam = GroupChat::where('group_id', '1099022' . $group->id)->first();
                        $members   = json_decode($groupteam->members);
                        foreach ($members as $key => $membe) {
                            if($membe != Auth::id()){
                                $user = User::where('id',$membe)->first();
                                MessageViewed::create(['product_id'=>$group->admin_id,'send_to'=>Auth::user()->id,'receiver'=>$membe,'is_group'=>1,'viewed'=>'0','group_id'=>'1099022'.$group->id]);
                            }
                        }



       return response()->json(['title'=>'Done','message' => 'Staff added successfully','type'=>'success']);
  }
      public function disputeMilestone(Request $request){
             extract($request->all());
             $admin = User::findOrFail(2);
             DisputeMilestone::updateOrCreate(
                 [
                     'disputed_milestone_id'=>$disputed_milestone_id,
                     'job_id'               =>$job_id
                 ],
                 [
                     'dispute_title'        =>$dispute_title,
                     'disputed_milestone_id'=>$disputed_milestone_id,
                     'enter_message'        =>$enter_message,
                     'outcome'              =>$outcome,
                     'job_id'               =>$job_id,
                     'job_offer_id'         =>$job_offer_id,
                     'in_dispute_chat'      => 1,
                     'status'               => 0,
                     'solve_by_buyer'       => 0,
                     'solve_by_seller'      => 0,
                      'decision'            => ''
                 ]);
             $id = $request->job_id;
             $job_offer_id = $request->job_offer_id;
             $image = '';
             $content = '';
             $item      = Job::where('id',$id)->first();
             $job_offer = JobOffer::where('id',$job_offer_id)->first();
             if (Auth::user()->hasRole('buyer')){
                 $sender_id = Auth::id();
                 $receiver  = $job_offer->staff_id;
             }elseif(Auth::user()->hasRole('seller')){
                 $sender_id = Auth::id();
                 $receiver  = $item->user_id;
             }
             $user = ['2',$sender_id,$receiver];
             $group  = GroupChat::updateOrCreate(
                 [
                     'admin_id'     => $item->id,
                     'sender_id'    => $sender_id,
                     'receiver'     => $receiver,
                     'milestone_id' => $request->disputed_milestone_id
                 ],[
                     'members'        => json_encode($user),
                     'sender_id'      => $sender_id,
                     'receiver'       => $receiver,
                     'order_by'       => time(),
                     'milestone_id'   => $request->disputed_milestone_id,
                     'dispute_status' => 0,
                     'decision'       => '',
                     'status'         => 1,
                     'self_resolved'         => 0,
                 ]
             );
          if ($group) {
              $jobDispute = JobMilestone::findOrFail($disputed_milestone_id);
              $data = [
                  'sender_name'    => Auth::user()->name,
                  'job_title'      => $item->project_title,
                  'email'          => User::find($item->user_id)->email,
                  'dispute_title'  => $jobDispute->title,
                  'dispute_amount' => $jobDispute->amount,
                  'role'           => Auth::user()->hasRole('seller') ? 'seller' : 'buyer',
                  'seller_email'   => User::find($item->assign_seller_id)->email,
                  'seller_name'    => User::find($item->assign_seller_id)->name,
              ];

              $buyerEmail     = $data['email'];
              $buyerTemplate  = 'static_email_templates.dispute_open';
              $sellerEmail    = $data['seller_email'];
              $sellerTemplate = 'static_email_templates.seller_dispute_open';
//              $sellerTemplate = 'static_email_templates.dispute_open';

              $sendMail = function ($recipientEmail, $template, $subject) use ($data) {
                  Mail::send($template, ['data' => $data], function ($message) use ($recipientEmail, $subject) {
                      $message->to($recipientEmail)
                          ->subject($subject);
                  });
              };

              if (Auth::user()->hasRole('seller')) {
                  $sendMail($sellerEmail, $sellerTemplate, 'Dispute Opened');
                  $sendMail($buyerEmail, $buyerTemplate, 'Dispute Opened By Seller');
              } else {

                  $sendMail($buyerEmail, $buyerTemplate, 'Dispute Opened');
                  $sendMail($sellerEmail, $sellerTemplate, 'Dispute Opened By Buyer');
              }

              \Log::info('Dispute Opened', $data);

//              $custom = Notification::create([
//                  'notifiable_id' => $admin->id,
//                  'notifiable_type' => 'App\User',
//                  'type' => 'disputeCreated',
//                  'data' => $data,
//              ]);
              $customData = $data;
              $customData['title'] = 'Dispute Opened';
                $customData['message'] = 'A new dispute has been opened by ' . Auth::user()->name . ' for the job: ' . $item->project_title;
                $customData['type'] = 'disputeCreated';
                event(new UserNotifyEvent($admin->id , $customData['type'],$customData));
                event(new UserNotifyEvent(User::find($item->user_id)->id, $customData['type'], $customData));
                event(new UserNotifyEvent(User::find($item->assign_seller_id)->id, $customData['type'], $customData));
//              $this->notificationRepo->createNotification($admin->id, 'App\User', 'disputeCreated', $data);
          }
          GroupChat::where('id',$group->id)->update(['group_id'=>'1099022'.$group->id.'']);
          Session::put('item_id',$item->id);
          Session::put('group_id','1099022'.$group->id.'');
          $requestData['product_id']  = $id;
          $content .=    '<div class="location_box ">';
          $content .=    '<span><i class="fa-solid fa-location-dot"></i></span>';
          $content .=    '<p>'.$request->enter_message.'</p>';
          $content .=    '</div>';
          $requestData['content']         = $request->enter_message;
          $requestData['to_user_id']      = '1099022'.$group->id.'';
          $requestData['from_user_id']    = Auth::user()->id;
          $requestData['viewed_by_receiver']    = 0;
          $requestData['status']    = 1;
          $messages      = Message::create($requestData);
          $groupteam = GroupChat::where('group_id','1099022'.$group->id.'')->first();
          $members   = json_decode($groupteam->members);
          foreach ($members as $key => $value) {
              if($value != Auth::id()){
                  $user = User::where('id',$value)->first();
                  MessageViewed::create(['product_id'=>$id,'send_to'=>Auth::user()->id,'receiver'=>$value,'is_group'=>1,'viewed'=>'0',
                      'group_id'=>'1099022'.$group->id.'','milestone_id'=>$request->disputed_milestone_id]);
              }
          }
          return response()->json(['title'=>'Done','message' => 'Disputed Milestone created successfully','type'=>'success','id'=>'1099022'.$group->id]);
      }

      public function staffChat($job_id){
          $manager       = [];
          $groups        = [];
          $company       = [];
          $task_manager  = [];
          $users         = [];
          $job           =  Job::where('id',$job_id)->first();
          $milestones_id = JobMilestone::where('job_id',$job_id)->pluck('id')->toArray();
           if (Auth::user()->hasRole('staff')){
              $groups = GroupChat::where(function($query) use ($milestones_id) {
                  $query->whereIn('milestone_id',$milestones_id)->where('show_group',1);
              })->orWhere(function ($query) use ($milestones_id) {
                  $query->whereIn('milestone_id',$milestones_id)->where('show_group',1);
//              })->orderBy('order_by','DESC')->get();
              })->orderBy('order_by','DESC')->get();

              return view("dashboard.staff.staff_chat",compact('users','groups','manager','company','task_manager','job_id','job'));
           }elseif(Auth::user()->hasRole('user')){
              $groups = GroupChat::where(function($query) use ($milestones_id){
                  $query->whereIn('milestone_id',$milestones_id)/*->where('show_group',1)*/;
              })->orWhere(function ($query) use ($milestones_id) {
                  $query->whereIn('milestone_id',$milestones_id)/*->where('show_group',1)*/;
              })->orderBy('order_by', 'DESC')->get();
              return view("dashboard.admin.admin_dispute_chat",compact('users','groups','manager','company','task_manager','job_id','job'));
           }
    }


    public function estimateJobOffer(Request $request){
//        return $request->all();
        extract($request->all());
        if (!isset($job_id) || empty($job_id) || $job_id == 0) {
            return back()->with(['title'=>'Error','message'=>'Error: Job ID not found','type'=>'error']);
        }//ends if
        $existingEstimateBidsCount = EstimateOffer::where('job_id', $job_id)->count();
        $isFirstBid = $existingEstimateBidsCount === 0;
        $isRange = $request->has('range_or_fixed') && $request->input('range_or_fixed') == 'yes';
        $offerData = [
            'job_id' => $job_id,
            'seller_id' => auth()->user()->id,
            'hide_bid_amount' => $request->input('hide_bid_amount', 'no'),
            'labour_expense' => $request->input('labour_expense'),
            'range_or_fixed' => $isRange ? 'yes' : 'no',
            'comment' => $request->input('description'),
        ];

        if ($isRange) {
            // Range is selected - use min/max amounts, clear fixed amount
            $offerData['min_amount'] = $request->input('min_amount') ?: 0;
            $offerData['max_amount'] = $request->input('max_amount') ?: 0;
            $offerData['amount'] = null; // Explicitly set to null to clear
        } else {
            // Fixed is selected - use single amount, clear range amounts
            $offerData['amount'] = $request->input('amount') ?: 0;
            $offerData['min_amount'] = null; // Explicitly set to null to clear
            $offerData['max_amount'] = null; // Explicitly set to null to clear
        }
//        $estimateOffer = EstimateOffer::updateOrCreate(
//            [
//                'job_id' => $job_id,
//                'seller_id' => auth()->user()->id
//            ],
//            [
//                'job_id'          => $job_id,
//                'seller_id'       => auth()->user()->id,
//                'hide_bid_amount' => $hide_bid_amount ?? 'no',
//                'labour_expense'  => $labour_expense??null,
//                'amount'          => $amount??0,
//                'range_or_fixed'  => $range_or_fixed ?? 'no',
//                'min_amount'      => $min_amount??0,
//                'max_amount'      => $max_amount??0,
//                'comment'         => $description??null,
//            ]
//        );
        $estimateOffer = EstimateOffer::updateOrCreate(
            [
                'job_id' => $job_id,
                'seller_id' => auth()->user()->id
            ],
            $offerData
        );
        $id = $job_id;
        $item = Job::where('id', $id)->first();
        if($item){
            $estimateData = [
                'project_name' => $item->project_title ?? '',
                'project_link' => url('posted_view/' . $item->id),
                'project_bid' => 'estimate',
                'staff_name' => auth()->user()->name,
                'buyer_email' => User::find($item->user_id)?->email ?? '<EMAIL>',
                'bid_amount' => $existingEstimateBidsCount ?? '',
            ];
            \Log::info('Estimate Data', [
                'estimateData' => $estimateData,
                'isFirstBid' => $isFirstBid,
                'estimateOffer' => $estimateOffer,
            ]);
            if ($estimateOffer && $isFirstBid) {
                try {
                    Mail::send('static_email_templates.bid_accept', ['info' => $estimateData], function ($message) use ($estimateData) {
                        $message->to($estimateData['buyer_email'])
                            ->cc('<EMAIL>')
                            ->subject('First bid posted');
                    });
                } catch (\Exception $e) {
                    \Log::error('Error sending FIRST Estimate bid email', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'estimateData' => $estimateData,
                    ]);
                }

            } elseif ($estimateOffer && !$isFirstBid) {
                try {
                    Mail::send('static_email_templates.bid_accept', ['info' => $estimateData], function ($message) use ($estimateData) {
                        $message->to($estimateData['buyer_email'])
                            ->cc('<EMAIL>')
                            ->subject('Another Bid Posted');
                    });
                } catch (\Exception $e) {

                }
            }

            $customData = $estimateData;
            $customData['title'] = 'Estimate Bid';
            $customData['message'] = 'Your estimate bid has been successfully created for the job: ' . $item->project_title;
            $customData['type'] = 'estimateBid';
            event(new UserNotifyEvent(User::find($item->user_id)?->id , $customData['type'],$customData));
            event(new UserNotifyEvent(auth()->user()->id , $customData['type'],$customData));
        }
        if ($estimateOffer){
            return redirect('explore_view_project/' . $job_id)->with(['title'=>'Done','message'=>'Estimate Offer stored successfully','type'=>'success']);
        }else{
            return back()->with(['title'=>'Error','message'=>'Error: Estimate Offer not stored','type'=>'error']);
        }

    }//ends function estimateJobOffer

    public function storeJobOffer(Request $request){
//        return $request->all();
//        die;
        extract($request->all());
        $existingBidsCount = JobOffer::where('job_id', $job_id)->count(); // previous
//        $existingBidsCount = JobOffer::where('job_id',$job_id)->whereNull('assign_seller_id')->count();
        $isFirstBid = $existingBidsCount == 0;
        $estimateBid = EstimateOffer::where('job_id', $job_id)
            ->where('seller_id', auth()->user()->id)
            ->first();

        if ($estimateBid) {
            try {
                $estimateBid->delete();
            } catch (\Exception $e) {

            }
        }


            $jobOffer = JobOffer::updateOrCreate(
                ['job_id' => $job_id, 'staff_id' => auth()->user()->id],
                [
                    'job_id'           => $job_id,
                    'staff_id'         => auth()->user()->id,
                    'hide_bid_amount'  => $hide_bid_amount ?? 'no',
                    'labour_expense'   => $labour_expense??null,
                    'material_expense' => $material_expense??null,
                    'amount'           => $amount??0,
                    'range_or_fixed'   => $range_or_fixed ?? 'no',
                    'min_amount'       => $min_amount??0,
                    'max_amount'       => $max_amount??0,
                    'comment'          => $description??null,
                ]
            );


        if ($jobOffer->wasRecentlyCreated) {
            $id = $job_id;
            $image = '';
            $content = '';
            $item = Job::where('id',$id)->first();
            $user = ['2', \Illuminate\Support\Facades\Auth::id(),$item->user_id];
            $groups = GroupChat::where(function ($query) use ($request) {
                $query->where('sender_id', Auth::user()->id)->where('admin_id',$request->id);
            })->orWhere(function ($query) use ($request) {
                $query->where('receiver', Auth::user()->id)->where('admin_id',$request->id);
            })->orderBy('order_by', 'DESC')->first();

            if (isset($groups->id)) {
                // GroupChat::where('id',$groups->id)->update(['status'=>0]);
                // Message::where('to_user_id',$groups->group_id)->update(['status'=>0]);
            }else{
                // $group =  GroupChat::updateOrCreate(['admin_id'=>$item->id,'sender_id'=>Auth::id(),'receiver'=>$item->user_id],
                    // ['members'=>json_encode($user),'sender_id'=>Auth::id(),'receiver'=>$item->user_id,'order_by'=>time()]);
                // GroupChat::where('id',$group->id)->update(['group_id'=>'1099022'.$group->id.'']);
                // Session::put('item_id',$item->id);
                // Session::put('group_id','1099022'.$group->id.'');
                // $requestData['product_id']      = $id;
                // $content .=    '<div class="location_box ">';
                // $content .=    '<span><i class="fa-solid fa-location-dot"></i></span>';
                // $content .=    '<p>'.Auth::user()->profile->city.','.Auth::user()->profile->state.','.Auth::user()->profile->country.'</p>';
                // $content .=    '</div>';
                // $requestData['content']            = $content;
                // $requestData['to_user_id']         = '1099022'.$group->id.'';
                // $requestData['from_user_id']       = Auth::user()->id;
                // $requestData['viewed_by_receiver'] = 0;
                // $messages  = Message::create($requestData);
                // $groupteam = GroupChat::where('group_id','1099022'.$group->id.'')->first();
                // $members   = json_decode($groupteam->members);
                // foreach ($members as $key => $value) {
                //     if($value != auth()->user()->id){
                //         $user = User::where('id',$value)->first();
                //         MessageViewed::create(['product_id'=>$id,'send_to'=>Auth::user()->id,'receiver'=>$value,'is_group'=>1,'viewed'=>'0','group_id'=>'1099022'.$group->id.'']);
                //     }
                // }
            }
        }//ends if
        $id = $job_id;
        $item = Job::where('id', $id)->first();
        $userId = User::find($item->user_id)->id ?? '';
        $authUser = auth()->user()->id ?? '';
        if ($item) {
            $data = [
                'project_name' => $item->project_title ?? '',
                'project_link' => url('posted_view/' . $item->id),
                'project_bid'  => 'fixed',
                'staff_name'   => auth()->user()->name,
                'staff_email'  => auth()->user()->email,
                'buyer_email'  => User::find($item->user_id)?->email ?? '<EMAIL>',
                'bid_amount'   => $existingBidsCount ?? '',
                'user_id'      => $item->user_id,
                'role'         => auth()->user()->hasRole('seller') ? 'seller' : 'buyer',
                'bid_amount'   => $amount ?? '0',
                'total_amount' => JobMilestone::where('staff_id', auth()->user()->id)->where('job_id', $job_id)->sum('amount'),

            ];
//                \Log::info('JOB OFFER DATA' , $data);
            if ($jobOffer && $isFirstBid) {
                try {
                    $buyerData = $data;
                    $buyerData['title'] = 'First Bid Posted';
                    $buyerData['message'] = 'A new bid has been posted for your project: ' . $data['project_name'];
                    $buyerData['type'] = 'jobOfferBidBuyer';
                    $buyerData['is_first'] = true;
                    $sellerData = $data;
                    $sellerData['title'] = 'Bid Posted';
                    $sellerData['message'] = 'You have posted a new bid for the project: ' . $data['project_name'];
                    $sellerData['type'] = 'jobOfferBidSeller';
//                    event(new UserNotifyEvent($userId,$data['type'], $buyerData));
//                    event(new UserNotifyEvent($authUser,$data['type'], $sellerData));

                    Mail::send('static_email_templates.bid_accept', ['info' => $data], function ($message) use ($data) {
                        $message->to($data['buyer_email'])
                            ->cc('<EMAIL>')
                            ->subject('First bid posted'); // this email is going to buyer
                    });
                    Mail::send('static_email_templates.seller_bid_request', ['info' => $data], function ($message) use ($data) {
                        $message->to($data['staff_email'])
                            ->cc('<EMAIL>')
                            ->subject('Bid Posted'); // this email is going to seller
                    });
                } catch (\Exception $e) {

                }

            } elseif ($jobOffer && !$isFirstBid) {
                try {
                    $buyerData = $data;
                    $buyerData['title'] = 'Another Bid Posted';
                    $buyerData['message'] = 'A new bid has been posted for your project: ' . $data['project_name'];
                    $buyerData['type'] = 'jobOfferBidBuyer';
                    $buyerData['is_first'] = false;
                    $sellerData = $data;
                    $sellerData['title'] = 'Bid Posted';
                    $sellerData['message'] = 'You have posted a new bid for the project: ' . $data['project_name'];
                    $sellerData['type'] = 'jobOfferBidSeller';
//                    event(new UserNotifyEvent(User::find($item->user_id)->id ?? '',$data['type'], $buyerData));
//                    event(new UserNotifyEvent(auth()->user()->id ?? '',$data['type'], $sellerData));
                    Mail::send('static_email_templates.bid_accept', ['info' => $data], function ($message) use ($data) {
                        $message->to($data['buyer_email'])
                            ->cc('<EMAIL>')
                            ->subject('Another Bid Posted');
                    });
                    Mail::send('static_email_templates.seller_bid_request', ['info' => $data], function ($message) use ($data) {
                        $message->to($data['staff_email'])
                            ->cc('<EMAIL>')
                            ->subject('Bid Posted');
                    });


                } catch (\Exception $e) {

                }

            }

        }
        if ($jobOffer){
            event(new UserNotifyEvent($userId,'jobOfferBidBuyer', $buyerData));
            event(new UserNotifyEvent($authUser,'jobOfferBidSeller', $sellerData));

            return redirect('explore_view_project/' . $job_id)->with(['title'=>'Done','message'=>'Job Offer stored successfully','type'=>'success']);
        }else{
            return back()->with(['title'=>'Error','message'=>'Error: Job Offer not stored','type'=>'error']);
        }//ends if
    }//ends function storeJobOffer


    // public function getUserNotifications()
    // {
    //  $notification = Message::where('to_user_id',Auth::user()->id)->where('viewed_by_receiver',0)->get();
    //  $count = Message::where('to_user_id',Auth::user()->id)->where('viewed_by_receiver',0)->count();
    //     return (string) view('website.ajax.user_notification',compact('notification','count'));
    // }
    // public function getAdminRewardNotification()
    // {
    //   $notification = RewardShare::where('admin_id',Auth::user()->id)->where('admin_view',0)->get();
    //     return (string) view('dashboard.ajax.reward_noti_admin',compact('notification'));
    // }
    // public function getuserRewardNotification()
    // {
    //      if (Auth::user()->roleId == 3){
    //         $notification = RewardShare::where('user_to_id',Auth::user()->id)->where('user_view',0)->get();
    //          return (string) view('dashboard.ajax.reward_noti_admin',compact('notification'));
    //    }
    // }

}
