<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
class Job extends Model
{
    use SoftDeletes;
    protected $table = 'jobs';
    protected $primaryKey = 'id';
    protected $guarded = [];
    protected $appends = ['job_question_answer'];

    public function user() {
        return $this->belongsTo(User::class);
    }//ends function user.
    public function assignSeller() {
        return $this->belongsTo(User::class,'assign_seller_id');
    }//ends function user.

    public function category() {
        return $this->belongsTo(JobCategory::class,'category_id');
    }//ends function category.

    public function subCategory() {
        return $this->belongsTo(JobSubcategory::class,'sub_category_id');
    }//ends function category.


    public function getjobQuestionAnswerAttribute()
    {
        $formattedAnswers = [];
        foreach (json_decode($this->dynamic_question_answer) as $questionId => $answerData) {
            $question = JobQuestion::find($questionId);
            if ($question) {
                $formattedAnswers[] = [
                    'question' => $question->question,
                    'value' => $answerData->val,
                ];
            }
        }
        return $formattedAnswers;
    }
    public function jobFiles() {
        return $this->hasMany(JobFile::class,'job_id');
    }//ends function jobFiles.

    public function jobAssignStaff() {
        return $this->belongsTo(User::class,'assign_staff_id');
    }//ends function jobAssignStaffs.

    public function jobAssignStaffDocuments() {
        return $this->hasMany(JobAssignStaffDocument::class,'job_id')->where('status',1);
    }//ends function jobAssignStaffs.

    public function jobAssignStaffMeasurements() {
        return $this->hasMany(JobAssignStaffMeasurement::class,'job_id')->where('status',1);
    }//ends function jobAssignStaffs.

    public function jobOffers() {
        return $this->hasMany(JobOffer::class,'job_id','id');
    }//ends function jobOffers.

    public function estimateOffers() {
        return $this->hasMany(EstimateOffer::class,'job_id');
    }//ends function estimateOffers.

    public function jobOffersPaid() {
        return $this->belongsTo(JobOffer::class,'id','job_id')->where('status_paid','paid');
    }//ends function jobOffers.

    public function jobMilestone() {
        return $this->hasMany(JobMilestone::class,'job_id','id');
    }//ends function jobMilestone.
    public function getChat(){
       return $this->belongsTo(GroupChat::class, 'id', 'admin_id')
                ->where(function ($query) {
                    $query->where('milestone_id',0)->where('sender_id', Auth::id())
                          ->orWhere('receiver', Auth::id());
                });
    }
    public function getSeller(){
        return $this->belongsTo(User::class,'assign_seller_id','id');
    }

    public function getJobReview(){
        return $this->hasMany(RatingReview::class,'job_id');
    }//ends function getJobReview.
    public function disputedJobs(){
        return $this->hasMany(DisputeMilestone::class,'job_id');
    }//ends function disputedJobs.

    public function myContactRequest(){
        return $this->hasOne(ContactRequest::class, 'job_id', 'id')
            ->where('seller_id', Auth::id());
    }

}//ends class Job.
