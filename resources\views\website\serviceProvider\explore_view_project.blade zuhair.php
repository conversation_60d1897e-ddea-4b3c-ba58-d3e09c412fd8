@extends('website.layout.master')
@push("css")
    <link rel="stylesheet" href="{{asset('plugins/components/dropify/dist/css/dropify.min.css')}}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />
    <style>
        .custom_justify{display: flex;justify-content: space-between;align-items: center}
        .custom_justify a{color:#4a4a4a;    font-family: 'LeagueSpartan-Regular';}
    </style>
@endpush
@section('content')
    <section class="hero_section header_video">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="{{ asset('website') }}/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="posted_view_page sp_explore_page" >
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    {{--                    <div class="top_results"><h5>Sort by:</h5>--}}
                    {{--                        <select name="cars" id="cars">--}}
                    {{--                            <option value="newest">Newest</option>--}}
                    {{--                            <option value="oldest">Oldest</option>--}}
                    {{--                            <option value="monthago">1 Month Ago</option>--}}
                    {{--                        </select>--}}
                    {{--                    </div>--}}
                </div>
                <div class="col-md-4">
                    <div class="listing_section">
                        <p style="color: #000;font-size:15px">Fixed Bid</p>
                        @if(isset($job->jobOffers) && !empty($job->jobOffers) && $job->jobOffers->count() > 0 )
                            @php
                                $userOffers = $job->jobOffers->where('staff_id', auth()->user()->id);
                                $otherOffers = $job->jobOffers->where('staff_id', '!=', auth()->user()->id);
                            @endphp
                            @foreach($userOffers as $offer)
                                <div class="post_list first_post staff_first_post fixed_bid_color">
                                    <div class="dflex">
                                        <div class="profile_name_inline">
                                            <div class="post_profile">
                                                <img src="{{ asset('storage/uploads/users') }}/{{$offer?->getStaffDetail?->profile->pic??''}}" alt="">
                                            </div>
                                            <h5>{{ $offer?->getStaffDetail->name ?? '---' }}</h5>
                                        </div>
                                        <h6>{{ $offer->created_at->format('d-m-Y H:i') }} </h6>
                                    </div>
                                    <div class="custom_justify">
                                        <div>
{{--                                            <h6 class="amount">Bid Amount</h6>--}}


                                            <h5>Labor Expense: ${{$offer->labour_expense ?? '0' }}</h5>
                                            <h5>Materials Expense: ${{$offer->material_expense ?? '0' }}</h5>
                                            <h5>Total Bid : ${{ $offer->getJobDetail->jobMilestone->where('staff_id',auth()->user()->id)->sum('amount') }}</h5>
{{--                                            @if($offer->range_or_fixed == 'no')--}}
{{--                                                <h5>Total Bid: $ {{ $offer->labour_expense ?? '0' }}+{{ $offer->material_expense ?? '0' }}</h5>--}}

                                            {{--                                            @else--}}
{{--                                                <h5>Total Bid: $ {{ $offer->amount ?? '0' }}</h5>--}}
{{--                                            @endif--}}
                                        </div>

                                        @if($offer->status != 'accepted')
                                            <a href="{{ url("services_bid_offer/$job->id") }}" class="view_link">View/Edit</a>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                            {{-- Display all other offers --}}
                            @foreach($otherOffers as $offer)
                                <div class="post_list first_post">
                                    <div class="dflex">
                                        <div class="profile_name_inline">
                                            <div class="post_profile">
                                                <img src="{{ asset('storage/uploads/users') }}/{{$offer?->getStaffDetail?->profile->pic??''}}" alt="">
                                            </div>
                                            <h5>{{ $offer->getStaffDetail->name ?? '---' }}</h5>
                                        </div>
                                        <h6>{{ $offer->created_at->format('d-m-Y H:i') }}</h6>
                                    </div>
                                    <div class="custom_justify">
                                        @if(auth()->user()->hasRole('buyer'))
                                            <div>
{{--                                                <h6 class="amount">Bid Amount</h6>--}}
                                                @if($offer->range_or_fixed == 'yes')
                                                    <h5>Total Bid: $ {{ $offer->min_amount ?? '0' }} - ${{ $offer->max_amount ?? '0' }}</h5>
                                                @else
                                                    <h5>Total Bid: $ {{ $offer->amount ?? '0' }}</h5>
                                                @endif
                                                <h5>Labor Expense: ${{$offer->labour_expense ?? '0' }}</h5>
                                                <h5>Materials Expense: ${{$offer->material_expense ?? '0' }}</h5>
                                            </div>
                                        @else
                                            @if($offer->hide_bid_amount == 'no')
                                                <div>
{{--                                                    <h6 class="amount">Bid Amount</h6>--}}
                                                    @if($offer->range_or_fixed == 'yes')
{{--                                                        <h5>Total Bid talha: $ {{ $offer->min_amount ?? '0' }} - ${{ $offer->max_amount ?? '0' }}</h5>--}}
                                                        <h5>Total Bid: $ {{ ($offer->labour_expense ?? 0) + ($offer->material_expense ?? 0) }} </h5>
                                                    @else

                                                        <h5>Total Bid: $ {{ ($offer->labour_expense ?? 0) + ($offer->material_expense ?? 0) }} </h5>

                                                    @endif
                                                    <h5>Labor Expense: ${{$offer->labour_expense ?? '0' }}</h5>
                                                    <h5>Materials Expense: ${{$offer->material_expense ?? '0' }}</h5>
                                                </div>
                                            @endif
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="post_list first_post staff_first_post">
                                <h6>No Fixed Bid Offer Found</h6>
                            </div>
                        @endif
                        {{--line between fixed and Estimate bid--}}
                            <div class="estimate_bid_section">
                                <p >Estimate Bid</p>
                            </div>
                        @if(isset($job->estimateOffers) && !empty($job->estimateOffers) && $job->estimateOffers->count() > 0)
                            @php
                                $userEstimateOffers = $job->estimateOffers->where('seller_id', auth()->user()->id);
                                $otherEstimateOffers = $job->estimateOffers->where('seller_id', '!=', auth()->user()->id);
                            @endphp
                            @foreach($userEstimateOffers as $offer)
                                <div class="post_list first_post staff_first_post estimate_bid_color">
                                    <div class="dflex">
                                        <div class="profile_name_inline">
                                            <div class="post_profile">
                                                <img src="{{ asset('storage/uploads/users') }}/{{$offer?->getSellerDetail?->profile->pic??''}}" alt="">
                                            </div>
                                            <h5>{{ $offer->getSellerDetail->name ?? '---' }}</h5>
                                        </div>
                                        <h6>{{ $offer->created_at->format('d-m-Y H:i') }} </h6>
                                    </div>
                                    <div class="custom_justify">

                                        @php
                                            $labour = (float) ($offer->labour_expense ?? 0);
                                            $priceDisplay = ($offer->min_amount && $offer->max_amount)
                                                ? '$' . number_format($labour + $offer->min_amount, 2) . ' --- $' . number_format($labour + $offer->max_amount, 2)
                                                : '$' . number_format($labour + ($offer->amount ?? 0), 2);
                                        @endphp

                                        <div>
{{--                                            <h6>{{$priceDisplay}}</h6>--}}
{{--                                            <h6 class="amount">Bid Amount</h6>--}}

                                            <h5>Labor Expense:  ${{$offer->labour_expense ?? '0' }}</h5>
                                            @if($offer->range_or_fixed == 'yes')
                                                <h5>Materials Expense: $ {{ $offer->min_amount ?? '0' }} - ${{ $offer->max_amount ?? '0' }}</h5>
                                                <h5>Total Bid: {{ $priceDisplay }}</h5>
                                            @else
                                                <h5>Materials Expense Bid: $ {{ $offer->amount ?? '0' }}</h5>
                                                <h5>Total Bid:  {{ $priceDisplay }}</h5>
                                            @endif
                                            @if(!empty($offer->material_expense))
                                            <h5>Materials Expense: ${{$offer->material_expense ?? '0' }}</h5>
                                            @endif
                                        </div>

                                        {{--                                                @if($offer->status != 'approved')--}}
                                        <a href="{{ url("estimate_bid_offer/$job->id") }}" id="editOrFixed" class="view_link">View/Edit</a>
                                        {{--                                                @endif--}}

                                    </div>
                                </div>
                            @endforeach

                            {{-- Display all other offers --}}
                            @foreach($otherEstimateOffers as $offer)
                                <div class="post_list first_post estimate_bid_color">
                                    <div class="dflex">
                                        <div class="profile_name_inline">
                                            <div class="post_profile">
                                                <img src="{{ asset('storage/uploads/users') }}/{{$offer?->getSellerDetail?->profile->pic??''}}" alt="">
                                            </div>
                                            <h5>{{ $offer->getSellerDetail->name ?? '---' }}</h5>
                                        </div>
                                        <h6>{{ $offer->created_at->format('d-m-Y H:i') }}</h6>
                                    </div>
                                    <div class="custom_justify">
                                        @if(auth()->user()->hasRole('buyer'))
                                            <div>
{{--                                                <h6 class="amount">Bid Amount</h6>--}}
                                                @if($offer->range_or_fixed == 'no')
                                                    <h5>Total Bid: $ {{ $offer->min_amount ?? '0' }} --- Buyer${{ $offer->max_amount ?? '0' }}</h5>
                                                @else
                                                    <h5>Total Bid: $ {{ $offer->amount ?? '0' }}</h5>
                                                @endif
                                                <h5>Labor Expense: ${{$offer->labour_expense ?? '0' }}</h5>
                                                <h5>Materials Expense: ${{$offer->material_expense ?? '0' }}</h5>
                                            </div>
                                        @else
                                            @if($offer->hide_bid_amount == 'no')
                                                <div>
                                                    @php
                                                        $labour = (float) ($offer->labour_expense ?? 0);
                                                        $otherPrice = ($offer->min_amount && $offer->max_amount)
                                                            ? '$' . number_format($labour + $offer->min_amount, 2) . ' --- $' . number_format($labour + $offer->max_amount, 2)
                                                            : '$' . number_format($labour + ($offer->amount ?? 0), 2);
                                                    @endphp
{{--                                                    <h6 class="amount">{{ $otherPrice }}</h6>--}}

                                                    <h5>Labor Expense: ${{$offer->labour_expense ?? '0' }}</h5>
                                                    @if($offer->range_or_fixed == 'yes')
                                                        <h5>Materials Expense: $ {{ $offer->min_amount ?? '0' }} ---Seller ${{ $offer->max_amount ?? '0' }}</h5>
                                                        <h5>Total Bid : {{ $otherPrice }} seelerr</h5>
                                                    @else
                                                        <h5>Materials Expense: $ {{ $offer->amount ?? '0' }}</h5>
                                                        <h5>Total Bid :{{ $otherPrice }} </h5>
                                                    @endif
                                                    @if(!empty($offer->material_expense))
                                                        <h5>Materials Expense: ${{$offer->material_expense ?? '0' }}</h5>
                                                    @endif
                                                </div>
                                            @endif
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="post_list first_post staff_first_post">
                                <h6>No Estimate Bid Offer Found</h6>
                            </div>
                        @endif
                    </div>
                    <div class="offer_contact_btn">
{{--                        @dd($job->estimateOffers()->where('seller_id',auth()->user()->id)->exists())--}}
{{--                        @if($job->jobOffers()->where('staff_id', auth()->user()->id)->exists() && $job->estimateOffers()->where('seller_id',auth()->user()->id)->exists())--}}
{{--                        @if($job->estimateOffers()->where('seller_id',auth()->user()->id)->exists())--}}
{{--                            <a href="{{ url("services_bid_offer/$job->id")}}" class="btn btn_blue btn_has_icon make_your_offer_start_fixed" id="make_fixed_bid">Make your Fixed Bid.--}}
{{--                                <div class="btn_icon">--}}
{{--                                    <i class="fa-solid fa-arrow-right"></i>--}}
{{--                                </div>--}}
{{--                            </a>--}}
{{--                        @endif--}}
                        @if(!$job->jobOffers()->where('staff_id', auth()->user()->id)->exists() && !$job->estimateOffers()->where('seller_id',auth()->user()->id)->exists())
{{--                            <a href="{{ url("services_bid_offer/$job->id")}}" class="btn btn_blue btn_has_icon make_your_offer_start">Make your Offer ---}}
{{--                                <div class="btn_icon">--}}
{{--                                    <i class="fa-solid fa-arrow-right"></i>--}}
{{--                                </div>--}}
{{--                            </a>--}}
                            <a href="#" class="btn btn_blue btn_has_icon make_your_offer_start" id="make_offer_btn">
                                Make your Offer
                                <div class="btn_icon">
                                    <i class="fa-solid fa-arrow-right"></i>
                                </div>
                            </a>
                        @endif
                        @if (isset($job->getChat->id) && $job->getChat->status == 0)
                            <button  disabled class="btn btn_black btn_has_icon">Request Pending <div class="btn_icon"> <i class="fa-solid fa-arrow-right"></i></div></button>
                        @elseif (isset($job->getChat->id) && $job->getChat->status == 1)
                            <a href="{{ route('service_provider_chat') }}/{{ $job->getChat->group_id }}" class="btn btn_black btn_has_icon">Message<div class="btn_icon"> <i class="fa-solid fa-arrow-right"></i></div></a>
                        @else
                            <a href="{{ route('request_contact') }}/{{ $job->id }}" class="btn btn_black btn_has_icon">Request Contact <div class="btn_icon"> <i class="fa-solid fa-arrow-right"></i></div></a>
                        @endif
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="row custom_rowGap">
                        <div class="col-md-12">
                            <div class="explore_card_user">
                                <div class="custom_categories">
                                    <a href="javascript:void(0)" class="" style="pointer-events: none; cursor: default;">
                                        Title :  {{ $job->project_title ?? '---'}} </a>
                                </div>
                                <div class="logo_and_name">
                                    <div class="logo_content_rating">
                                        <div class="img_container">
                                            <img src="{{ asset('website') }}/assets/images/inside_logo.png" alt="">
                                        </div>
                                        <h3>{{$job->user->name??'---'}}</h3>
                                    </div>
                                    <div class="rating_star">
                                        <div class="seller_rating">
                                            <span class="fa fa-star @if($job->user->ratingSum >= 1) checked @endif"></span>
                                            <span class="fa fa-star @if($job->user->ratingSum >= 2) checked @endif"></span>
                                            <span class="fa fa-star @if($job->user->ratingSum >= 3) checked @endif"></span>
                                            <span class="fa fa-star @if($job->user->ratingSum >= 4) checked @endif"></span>
                                            <span class="fa fa-star @if($job->user->ratingSum >= 5) checked @endif"></span>
                                            <h6>{{ $job->user->ratingSum }} Stars</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="card_info">
                                <h5>Email: <span> {{ $job->user->email ?? '---' }} </span></h5>
                                <h5>Posted On: <span> {{ $job->created_at->format('d-m-y') ??'---'}} </span></h5>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="custom_categories">
                                <a href="javascript:void(0)" class="" style="pointer-events: none; cursor: default;">
                                    {{$job->category->name ?? '----'}} / {{$job->subCategory->name ?? '----'}}</a>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <h5>Budget:</h5>
                            <h3>${{$job->project_budget_min??'0'}} - ${{$job->project_budget_max??'0'}}</h3>
                        </div>

                        <div class="col-md-12">
                            <h5>Photos</h5>
                            <div class="row custom_row">
                                @if(!empty($job->jobFiles))
                                    @foreach($job->jobFiles as $file)
                                        @php
                                            $fileUrl = $file->file ?? '---';
                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                        @endphp
                                        <div class="col-md-3 col-sm-4 col-6">
                                            @if($isImage)
                                                <a href="{{ $fileUrl }}" data-fancybox="gallery">
                                                    <div class="project_photos">
                                                        <img src="{{ $fileUrl }}" alt="Job File" class="img-fluid" loading="lazy">
                                                    </div>
                                                </a>
                                            @elseif($isVideo)
                                                <a href="{{ $fileUrl }}" data-fancybox="gallery" data-type="video">
                                                    <div class="project_photos">
                                                        <video controls preload="metadata" class="img-fluid video-player">
                                                            <source src="{{ $fileUrl }}" type="video/{{ $extension === 'mov' ? 'quicktime' : $extension }}">
                                                            Your browser does not support this video format. <a href="{{ $fileUrl }}" download>Download the video</a>
                                                        </video>
                                                    </div>
                                                </a>
                                            @else
                                                <div class="project_photos">
                                                    <p>Unsupported file type: {{ $extension }}</p>
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>
                        @if(!empty($job->jobQuestionAnswer))
                            @foreach($job->jobQuestionAnswer as $element)
                                <div class="col-md-12">
                                    <h5>{{ $element['question'] ?? '----' }}</h5>
                                    @php
                                        $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                    @endphp
                                    <a href="{{ asset('website') }}/{{ $value }}" data-fancybox="gallery">
                                        <div class="questionnaire_img">
                                            @if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp|heic)$/i', $value))
                                                <img src="{{ asset('website') }}/{{ $value }}" style="max-width: 100%;">
                                            @else
                                        </div>
                                    </a>
                                         <h6>{{ is_string($value) ? $value : '----' }}</h6>
                                         @endif
                                </div>
                            @endforeach
                        @endif
{{--                        @if(isset($job->jobAssignStaffMeasurements) && !$job->jobAssignStaffMeasurements->isEmpty())--}}
{{--                            <div class="col-md-12">--}}
{{--                                <div class="project_detail">--}}
{{--                                    <div class="">--}}
{{--                                        <h3>Staff Further Details</h3>--}}
{{--                                    </div>--}}
{{--                                    <div class="project_scope">--}}
{{--                                        <h5>Measurements : </h5>--}}
{{--                                        @forelse($job->jobAssignStaffMeasurements as $measurement)--}}
{{--                                            <h6>Name:{{$measurement->name??'-'}}, Number:{{$measurement->number??'-'}}, Unit:{{$measurement->unit??'-'}}</h6>--}}
{{--                                        @empty--}}
{{--                                            <h6> No Staff Measurements </h6>--}}
{{--                                        @endforelse--}}
{{--                                        <h5>Specifications</h5>--}}
{{--                                        <h6>{!! $job->staff_specifications ?? 'N/A' !!}</h6>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}

                                @php
                                $hasDocuments = $job->jobAssignStaffDocuments && $job->jobAssignStaffDocuments->isNotEmpty()
                                @endphp
{{--                            @if(isset($job->jobAssignStaffDocuments) && !empty($job->jobAssignStaffDocuments))--}}
                            @if($hasDocuments)
                                <div class="col-md-12">
                                    <div class="project_detail">
                                        <div class="">
                                            <h3>Staff Further Details</h3>
                                        </div>
                                        <div class="">
                                            <h5>Media</h5>
                                        </div>
                                        <div class="media_download">
                                            @foreach($job->jobAssignStaffDocuments as $file)
                                                @php
                                                    $fileUrl = $file->image ?? '---';
                                                    $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                @endphp

                                                @if($isImage)
                                                    <div class="images_with_text">
                                                        <a href="{{ $fileUrl }}" data-fancybox="gallery">
                                                            <div class="custom_images">
                                                                <img src="{{ $fileUrl }}" alt="Job File" class="">
                                                            </div>
                                                        </a>
                                                        <p>{{$file->name??''}}</p>
                                                    </div>
                                                @elseif($isVideo)
                                                    <div class="images_with_text">
                                                        <a href="{{ $fileUrl }}" data-fancybox="gallery">
                                                            <div class="custom_images staff_video">
                                                                <div class="downloadable_video">
                                                                    <video controls preload="metadata" class="video-player">
                                                                        <source src="{{ $fileUrl }}" type="video/{{ $extension === 'mov' ? 'quicktime' : $extension }}">
                                                                        Your browser does not support this video format.
                                                                    </video>
                                                                </div>
                                                            </div>
                                                        </a>
                                                        <p>{{$file->name??''}}</p>
                                                    </div>
                                                @else
                                                    <div class="custom_images">
                                                        <p>Unsupported file type: {{ $extension }}</p>
                                                    </div>
                                                @endif
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endif

{{--                        @endif--}}
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
    <script>
        $(document).ready(function() {
            $('[data-fancybox="gallery"]').fancybox({
                protect: false,
                clickOutside: false,
                closeExisting: false,
            });
        });
        const fixedBidBtn = document.getElementById("editOrFixed");

        if (fixedBidBtn) {
            fixedBidBtn.addEventListener("click", function (e) {
                e.preventDefault();

                Swal.fire({
                    title: 'What type of bid do you want to make?',
                    showCancelButton: true,
                    showDenyButton: true,
                    confirmButtonText: 'Fixed Bid',
                    denyButtonText: 'Edit Estimate',
                    cancelButtonText: 'Cancel',
                    icon: 'question'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = '{{ url("services_bid_offer/$job->id") }}';
                    } else if (result.isDenied) {
                        window.location.href = '{{ url("estimate_bid_offer/$job->id") }}';
                    }
                });
            });
        }

        const offerBtn = document.getElementById("make_offer_btn");
        if (offerBtn) {
            offerBtn.addEventListener("click", function(e) {
                e.preventDefault();
                Swal.fire({
                    title: 'Do you want to make a Fixed Bid or Estimate?',
                    showCancelButton: true,
                    confirmButtonText: 'Fixed Bid',
                    cancelButtonText: 'Estimate',
                    icon: 'question'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = '{{ url("services_bid_offer/$job->id") }}';
                    } else if (result.dismiss === Swal.DismissReason.cancel) {
                        window.location.href = '{{ url("estimate_bid_offer/$job->id") }}';
                    }
                });
            });
        }
        {{--document.getElementById("make_offer_btn").addEventListener("click", function(e) {--}}
        {{--    e.preventDefault(); // Prevent the default link behavior--}}
        {{--    // Trigger SweetAlert2 with two buttons--}}
        {{--    Swal.fire({--}}
        {{--        title: 'Do you want to make a Fixed Bid or Estimate?',--}}
        {{--        showCancelButton: true,--}}
        {{--        confirmButtonText: 'Fixed Bid',--}}
        {{--        cancelButtonText: 'Estimate',--}}
        {{--        icon: 'question'--}}
        {{--    }).then((result) => {--}}
        {{--        if (result.isConfirmed) {--}}
        {{--            // If user clicked "Fixed Bid", redirect to the Fixed Bid URL--}}
        {{--            window.location.href = '{{ url("services_bid_offer/$job->id") }}';--}}
        {{--        } else if (result.dismiss === Swal.DismissReason.cancel) {--}}
        {{--            // If user clicked "Estimate", redirect to the Estimate URL--}}
        {{--            window.location.href = '{{ url("estimate_bid_offer/$job->id") }}';--}}
        {{--        }--}}
        {{--    });--}}
        {{--});--}}
    </script>
@endpush
