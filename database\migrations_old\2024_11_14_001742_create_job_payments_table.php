<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateJobPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('job_payments', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->string('amount')->nullable();
            $table->string('seller_id')->nullable();
            $table->string('job_id')->nullable();
            $table->string('stripe_recharge_id')->nullable();
            $table->string('captured')->nullable();
            $table->string('charge_id')->nullable();
            $table->string('receipt_url')->nullable();
            $table->string('captured_status')->nullable();
            $table->string('buyer_id')->nullable();
            $table->text('stripe_json_data')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('job_payments');
    }
}
