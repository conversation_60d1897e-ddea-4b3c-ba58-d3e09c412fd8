<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
            $table->text('name')->nullable();
            $table->text('group_mes')->nullable();
            $table->text('from_user_id')->nullable();
            $table->text('to_user_id')->nullable();
            $table->text('content')->nullable();
            $table->text('status')->nullable();
            $table->text('viewed_by_sender')->nullable();
            $table->text('viewed_by_receiver')->nullable();
            $table->text('product_id')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('messages');
    }
}
